<?php
public function getProductList(Request $request, $id)
	{
		try {
			//code...
			if($this->canAccess || $this->canUpdate) {
				$viewShowData = "";
				$d = json_decode(file_get_contents('php://input'));
	// dd($d);
				$tableName = "iPRO_ShopProduct";
				$tableName2 = "iPRO_Coupon";
				$shopID = $id;
	
				$params = ["shopid" => $id];

				$qry = "SELECT SP.`ShopProductID`, SP.`ShopID`, SP.`ShopProductName`, SP.`ShopProductCode`, SP.`DateActiveStart`, SP.`DateActiveEnd`, SP.`ShopProductStatus`, SP.`ShopProductType`, SP.`ShopProductAge`, SP.`ShopProductUnitPrice`, SP.`ShopProductTitle`, SP.`OrderingNo`, SP.`IsUseImageThumbnail`, SP.`ImageThumbnailURL`, SP.`IsUseImagesDetail`, SP.`ImagesDetailURL`, SP.`CouponBucketID`, GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode";

				$joins = " FROM `iPRO_ShopProduct` SP 
						INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` 
						LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID";
				// $qry = "SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SP.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode ";

				$conditions = " WHERE SP.`ShopProductStatus` <> 'Archive' AND SP.`ShopID` = :shopid";
				$orderby = " ORDER BY SP.OrderingNo ASC";
				$groupby = " GROUP BY SP.`ShopProductID`, SP.`ShopProductName`, SP.`ShopProductCode`, SP.`DateActiveStart`, SP.`DateActiveEnd`, SP.`ShopProductStatus`, SP.`ShopProductType`, SP.`ShopProductAge`, SP.`ShopProductUnitPrice`, SP.`ShopProductTitle`, SP.`OrderingNo`, SP.`IsUseImageThumbnail`, SP.`ImageThumbnailURL`, SP.`IsUseImagesDetail`, SP.`ImagesDetailURL`, CT.CreditCode";
				
				// $qry = "SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SP.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode 
				// 	FROM `iPRO_ShopProduct` SP INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID";
				// $orderby = " ORDER BY SP.OrderingNo ASC";
	
				// $condition = " WHERE SP.`ShopProductStatus` <> 'Archive' AND FIND_IN_SET(C.ShopCategoryID, SP.Categories) AND SP.`ShopID` = :shopid";
				// $groupby = " GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SP.`OrderingNo`, CT.CreditCode";

				$getAll = true;
				if(isset($d)){
					if(!empty($d->ShopProductStatus) && $d->ShopProductStatus != "all") {
						$getAll = false;
						$conditions .= " AND SP.`ShopProductStatus` = :shopstatus";
						$params["shopstatus"] = $d->ShopProductStatus;
					}
	
					if(!empty($d->ActiveDateRange) && !empty($d->DateActiveStart)) {
						$aStartDate = date($d->DateActiveStart);
						$condition = $condition." AND SP.`DateActiveStart` >= :datestart";
						$params["datestart"] = $aStartDate;
					}
					if(!empty($d->ActiveDateRange) && !empty($d->DateActiveEnd)) {
						$aEndDate = date($d->DateActiveEnd);
						$condition = $condition." AND SP.`DateActiveStart` <= :dateend";
						$params["dateend"] = $aEndDate;
					}

	
					// if(!empty($d->Categories)) {
					// 	$getAll = false;
					// 	$catID = $d->Categories->ShopCategoryID;
					// 	$qry = "SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SC.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode FROM `iPRO_ShopProduct_Category` SC INNER JOIN `iPRO_ShopProduct` SP ON SC.`ShopProductID` = SP.`ShopProductID` INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID";
	
					// 	$condition = $condition." AND SC.`ShopCategoryID` = :shopcatid";
					// 	$params["shopcatid"] = $catID;
	
					// 	$groupby = " GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SC.`OrderingNo`, CT.CreditCode";
	
					// 	$orderby = " ORDER BY SC.OrderingNo ASC";
					// }					
	
					if(isset($d->OrderBy)) {
						$getAll = false;
						if($d->OrderBy == "adate_desc"){
							$orderby = " ORDER BY SP.`DateActiveStart` DESC ";
						}
						if($d->OrderBy == "adate_asc"){
							$orderby = " ORDER BY SP.`DateActiveStart` ASC";
						}
						if($d->OrderBy == "mobile_order" && empty($d->Categories)){
							$orderby = " ORDER BY SP.`OrderingNo` ASC";
						}
					}

					if (!empty($d->Categories)) {
						$joins .= " INNER JOIN `iPRO_ShopProduct_Category` SC ON SC.`ShopProductID` = SP.`ShopProductID`";
						$conditions .= " AND SC.`ShopCategoryID` = :shopcatid";
						$params["shopcatid"] = $d->Categories->ShopCategoryID;
					}
					
					// ตรวจสอบ ProductGroups
					if (!empty($d->ProductGroups)) {
						// $joins .= " INNER JOIN `iPRO_ShopProduct_ProductGroup` PGP ON PGP.`ShopProductID` = SP.`ShopProductID`";
						// $conditions .= " AND PGP.`ProductGroupID` = :productgroupid";
						// $params["productgroupid"] = $d->ProductGroups->ProductGroupID;
					}
					// condition .= " AND SP.`ShopProductStatus` = :shopstatus";
					// 	$params["shopstatus"] = $d->ShopProductStatus;
					
					// $query = $qry . $joins . $conditions . $groupby . $orderby;
					// // dump($query);
					// dump(DB::select(DB::raw($query), $params) );
					// var_dump($testDD);

					// $conditions .= " AND SP.`ShopProductStatus` = $d->ShopProductStatus";
					// $params["shopstatus"] = $d->ShopProductStatus;
					// $query = $qry . $joins . $conditions . $groupby . $orderby;
					// dump($query);
				}
				if($getAll) {
					$viewShowData = DB::connection('mysql')
								->table($tableName)
								->leftJoin('iCASH_Credit_Setting', 'iCASH_Credit_Setting.CreditID', '=', 'iPRO_ShopProduct.CreditSettingID')
								->join('iPRO_ShopCategory', 'iPRO_ShopCategory.ShopID', '=', 'iPRO_ShopProduct.ShopID')
								->leftJoin('iPRO_ShopProduct_ProductGroup', 'iPRO_ShopProduct_ProductGroup.ShopProductID', '=', 'iPRO_ShopProduct.ShopProductID')
								->leftJoin('iPRO_ShopProductGroup', 'iPRO_ShopProductGroup.ShopProductGroupID', '=', 'iPRO_ShopProduct_ProductGroup.ShopProductGroupID')
								->where('iPRO_ShopProduct.ShopID', $shopID)
								->whereRaw("FIND_IN_SET(iPRO_ShopCategory.ShopCategoryID, iPRO_ShopProduct.Categories)")
								->selectRaw('GROUP_CONCAT(DISTINCT iPRO_ShopCategory.ShopCategoryName) as ShopCategoryName,iPRO_ShopProductGroup.ShopProductGroupID,iPRO_ShopProduct.*,iCASH_Credit_Setting.CreditCode')
								->groupBy('iPRO_ShopProduct.ShopProductID', 'iPRO_ShopProduct.ShopProductCode','iPRO_ShopProduct.ShopProductName')
								->orderBy('OrderingNo')->get();

					if(!empty($d->ProductGroups)) {
						// $getAll = false;
						$proGroupID = (int)$d->ProductGroups->ShopProductGroupID;
						// dump($proGroupID);
						$viewShowData = $viewShowData->where('iPRO_ShopProductGroup.ShopProductGroupID',$proGroupID)->get();
					} else {
						$viewShowData = $viewShowData->get();
					}

					$idList = [];
					foreach ($viewShowData as $key => $value) {
						// dump($value->ShopProductID);
						$data = DB::table('iPRO_ShopProductGroup')->where("ShopProductGroupID",$value->ShopProductGroupID)->selectRaw('GROUP_CONCAT(iPRO_ShopProductGroup.ShopProductGroupName) as ShopProductGroupName')->first();
						$value->ShopProductGroupName = null;
						// // array_push($idList,$data);
						// dump($data);
						if($data->ShopProductGroupName){
							$value->ShopProductGroupName = $data->ShopProductGroupName;
						}
					}
					// dd($idList);
					// $viewShowData = DB::connection('mysql')
					// 	->table($tableName)
					// 	->leftJoin('iCASH_Credit_Setting', 'iCASH_Credit_Setting.CreditID', '=', 'iPRO_ShopProduct.CreditSettingID')
					// 	->join('iPRO_ShopCategory', 'iPRO_ShopCategory.ShopID', '=', 'iPRO_ShopProduct.ShopID')
					// 	->leftJoin('iPRO_ShopProduct_ProductGroup', 'iPRO_ShopProduct_ProductGroup.ShopProductID', '=', 'iPRO_ShopProduct.ShopProductID')
					// 	->leftJoin('iPRO_ShopProductGroup', 'iPRO_ShopProductGroup.ShopProductGroupID', '=', 'iPRO_ShopProduct_ProductGroup.ShopProductGroupID')
					// 	->where('iPRO_ShopProduct.ShopID', $shopID)
					// 	->where(function ($query) {
					// 		$query->whereRaw('FIND_IN_SET(iPRO_ShopCategory.ShopCategoryID, iPRO_ShopProduct.Categories)')
					// 			->whereRaw('FIND_IN_SET(iPRO_ShopProductGroup.ShopProductGroupID,iPRO_ShopProduct_ProductGroup.ShopProductGroupID)');
					// 	})
					// 	->selectRaw('GROUP_CONCAT(DISTINCT iPRO_ShopCategory.ShopCategoryName) as ShopCategoryName,GROUP_CONCAT(DISTINCT iPRO_ShopProductGroup.ShopProductGroupName) as ShopProductGroupName, iPRO_ShopProduct.*,iCASH_Credit_Setting.CreditCode')
					// 	->groupBy('iPRO_ShopProduct.ShopProductID', 'iPRO_ShopProduct.ShopProductCode','iPRO_ShopProduct.ShopProductName')
					// 	->orderBy('OrderingNo')
					// 	->get();
				} else {
					//qry
					

					$query = $qry.''.$conditions.''.$groupby.''.$orderby;
					// $query = $qry . $joins . $conditions . $groupby . $orderby;
					//var_dump($query); dd();
					$viewShowData = DB::select(DB::raw($query), $params); 
					
					//var_dump($query);
				}
				
				if(count($viewShowData) > 0) {
					$helper = new XssHelper();
					$lans = Config::get('app.languages');
					$emptyTest = [];
					if(isset($lans)) {
						foreach ($lans as $lan) {
							$emptyTest[strtoupper($lan)] = "";
						}
					}
	
					$lan = strtoupper($this->lan);
					foreach ($viewShowData as $key => $product) {
	
						$product->couponAmount = null;
						
						$product->ShopProductName = html_entity_decode($product->ShopProductName);
						
						if(isset($product->ShopCategoryName)) {
							//bring out empty if format in db is wrong
							$check = $this->json_validate($product->ShopCategoryName);
							if(!$check) {
								$product->ShopCategoryName = $emptyTest;
							} else {
								$product->ShopCategoryName = strip_tags(html_entity_decode($helper->cleanInputForHTML($product->ShopCategoryName)));
							}
						}
						
						// $product->ShopProductGroupName = html_entity_decode($product->ShopProductGroupName);
						// if(isset($product->ShopProductGroupName)) {
						// 	//bring out empty if format in db is wrong
						// 	$check = $this->json_validate($product->ShopProductGroupName);
						// 	if(!$check) {
						// 		$product->ShopProductGroupName = $emptyTest;
						// 	} else {
						// 		$product->ShopProductGroupName = strip_tags(html_entity_decode($helper->cleanInputForHTML($product->ShopProductGroupName)));
						// 	}
						// }
					
						$product->hasThumbnailImage = false;
						$product->hasDetailImage = false;
	
						if(isset($product->ImageThumbnailURL)) {
							$thumURL = (array) json_decode($product->ImageThumbnailURL);
							$product->ThumbnailUrl = isset($thumURL[$lan])? $thumURL[$lan]: "";
							$product->hasThumbnailImage = true;
						} 
	
						if(isset($product->ImagesDetailURL)) {
							$detailURL = (array) json_decode($product->ImagesDetailURL);
							$product->DetailUrl = isset($detailURL[$lan]) ?  $detailURL[$lan]: "";
							$product->hasDetailImage = true;
						} 
					}
					
				}
 
				$logger = LoggerService::init();
				$this->grayLog($request, $viewShowData, 'response', $logger);
				return $viewShowData;
			} else {
				return response('Access Denied.', 403);
			}
		} catch (\Throwable $th) {
			//throw $th;
			// dump($th->getMessage());
		}	
	}


SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SP.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode,GROUP_CONCAT(SPG.ShopProductGroupName) AS ShopProductGroupName FROM `iPRO_ShopProduct` SP
INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` 
LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID
LEFT JOIN `iPRO_ShopProduct_ProductGroup` PGP ON PGP.`ShopProductID` = SP.`ShopProductID` 
LEFT JOIN `iPRO_ShopProductGroup` SPG ON SPG.`ShopProductGroupID` = PGP.`ShopProductGroupID` 
INNER JOIN `iPRO_ShopProduct_Category` SC ON SC.`ShopProductID` = SP.`ShopProductID` 
WHERE SP.`ShopProductStatus` <> 'Archive' AND FIND_IN_SET(C.ShopCategoryID, SP.Categories) AND SP.`ShopID` = :shopid AND SC.`ShopCategoryID` = :shopcatid GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SC.`OrderingNo`, CT.CreditCode 
ORDER BY SC.OrderingNo ASC

SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SP.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode,GROUP_CONCAT(SPG.ShopProductGroupName) AS ShopProductGroupName FROM `iPRO_ShopProduct` SP
INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` 
LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID
LEFT JOIN `iPRO_ShopProduct_ProductGroup` PGP ON PGP.`ShopProductID` = SP.`ShopProductID` 
LEFT JOIN `iPRO_ShopProductGroup` SPG ON SPG.`ShopProductGroupID` = PGP.`ShopProductGroupID` INNER JOIN `iPRO_ShopProduct_Category` SC ON SC.`ShopProductID` = SP.`ShopProductID` WHERE SP.`ShopProductStatus` <> 'Archive' AND FIND_IN_SET(C.ShopCategoryID, SP.Categories) AND SP.`ShopID` = :shopid AND SC.`ShopCategoryID` = :shopcatid GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SC.`OrderingNo`, CT.CreditCode 
ORDER BY SC.OrderingNo ASC


x1 y1 x2 y2

(define-struct rectangle (x1 y1 x2 y2))

(define (check-overlap rect1 rect2)
  (if (are-rectangles-valid? rect1 rect2)
      (cond
        [(>= (rectangle-x1 rect2) (rectangle-x2 rect1)) ; ถ้า rect2 อยู่ขวากว่า rect1
         (display "No overlap between the rectangles")]
        [(<= (rectangle-x2 rect2) (rectangle-x1 rect1)) ; ถ้า rect2 อยู่ซ้ายกว่า rect1
         (display "No overlap between the rectangles")]
        [(>= (rectangle-y1 rect2) (rectangle-y2 rect1)) ; ถ้า rect2 อยู่สูงกว่าหรือเท่ากับ rect1
         (display "No overlap between the rectangles")]
        [(<= (rectangle-y2 rect2) (rectangle-y1 rect1)) ; ถ้า rect2 อยู่ต่ำกว่าหรือเท่ากับ rect1
         (display "No overlap between the rectangles")]
        [else (display "Rectangles overlap")] ; ถ้าทุกเงื่อนไขไม่ตรงกัน แสดงว่ามีการทับซ้อน
      )
    (display "")
  )
)

; ฟังก์ชันตรวจสอบความถูกต้องของข้อมูลสี่เหลี่ยม
(define (are-rectangles-valid? rect1 rect2)
  (cond
    [(not (rectangle? rect1)) (display "The first rectangle is invalid.") #f]
    [(not (rectangle? rect2)) (display "The second rectangle is invalid.") #f]
    [(not (< (rectangle-x1 rect1) (rectangle-x2 rect1))) (display "Invalid x1 and x2 for the first rectangle.") #f]
    [(not (< (rectangle-y1 rect1) (rectangle-y2 rect1))) (display "Invalid y1 and y2 for the first rectangle.") #f]
    [(not (< (rectangle-x1 rect2) (rectangle-x2 rect2))) (display "Invalid x1 and x2 for the second rectangle.") #f]
    [(not (< (rectangle-y1 rect2) (rectangle-y2 rect2))) (display "Invalid y1 and y2 for the second rectangle.") #f]
    [else #t]
  )
)




(struct rectangle (x1 y1 x2 y2)) ;struct ของสี่เหลี่ยม เรียงจาก x1 y1 x2 y2 ตามลำดับ

;หาว่า retangle สอรูปวางซ้อนทับกันไหมในกราฟสองมิติ
(define (check_overlap_rectangle first_rectangle second_rectangle)
  (if (is_rectangle_condition_valid first_rectangle second_rectangle)
      (cond
        [(>= (rectangle-x1 second_rectangle) (rectangle-x2 first_rectangle)) ;ถ้า x ที่น้อยที่สุดของ Rec2 มากกว่าหรือเท่ากับ x ที่มากที่สุดของ Rec1 สี่เหลี่ยมทั้งสองจะไม่มีทางซ้อนกัน
         (println "These rectangles are not overlapped")] 
        [(<= (rectangle-x2 second_rectangle) (rectangle-x1 first_rectangle)) ;ถ้า x ที่มากที่สุดของ Rec2 น้อยกว่าหรือเท่ากับ x ที่น้อยที่สุดของ Rec1 สี่เหลี่ยมทั้งสองจะไม่มีทางซ้อนกัน
         (println "These rectangles are not overlapped")]
        [(>= (rectangle-y1 second_rectangle) (rectangle-y2 first_rectangle)) ;เช่นเดียวกับแกน x ถ้า y ที่น้อยที่สุดของ Rec2 มากกว่าหรือเท่ากับ y ที่มากที่สุดของ Rec1 สี่เหลี่ยมทั้งสองจะไม่มีทางซ้อนกัน
         (println "These rectangles are not overlapped")]
        [(<= (rectangle-y2 second_rectangle) (rectangle-y1 first_rectangle)) ;เช่นเดียวกับแกน x ถ้า y ที่มากที่สุดของ Rec2 น้อยกว่าหรือเท่ากับ y ที่น้อยที่สุดของ Rec1 สี่เหลี่ยมทั้งสองจะไม่มีทางซ้อนกัน
         (println "These rectangles are not overlapped")]
        [else (println "These rectangles are overlapped")] ;และถ้าผ่านเงื่อนไขทั้งหมดมาได้ แสดงว่าสี่เหลี่ยมทั้งสองมีส่วนที่ทับซ้อนกัน
      )
  (print "")
  )
)

; Validator function เพื่อตรวจสอบข้อมูล argument ว่าเป็นไปตามที่เราต้องการไหม
(define (is_rectangle_condition_valid first_rectangle second_rectangle)
  (cond
    [(false? (rectangle? first_rectangle)) (println "first_rectangle is not rectangle") #f]
    [(false? (rectangle? second_rectangle)) (println "second_rectangle is not rectangle") #f]
    [(false? (< (rectangle-x1 first_rectangle) (rectangle-x2 first_rectangle))) (println "first_rectangle x1 must be less than x2 (rectangle (x1 y1 x2 y2)") #f]
    [(false? (< (rectangle-y1 first_rectangle) (rectangle-y2 first_rectangle))) (println "first_rectangle y1 must be less than y2 (rectangle (x1 y1 x2 y2)") #f]
    [(false? (< (rectangle-x1 second_rectangle) (rectangle-x2 second_rectangle))) (println "second_rectangle x1 must be less than x2 (rectangle (x1 y1 x2 y2)") #f]
    [(false? (< (rectangle-y1 second_rectangle) (rectangle-y2 second_rectangle))) (println "second_rectangle y1 must be less than y2 (rectangle (x1 y1 x2 y2)") #f]
    [else #t]
  )
)