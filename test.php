<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\YourModel;

class ExampleController extends Controller
{
    public function insert(Request $request)
    {
        try {
            $data = $request->all();
            $newRecord = YourModel::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => $data['password'],
            ]);

            return response()->json([
                'status' => true,
                'code' => 201,
                'message' => 'Data inserted successfully!',
            ], 201);

        } catch (\Exception $e) {
            // Handle error
            return response()->json([
                'success' => false,
                'message' => 'Failed to insert data: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getData(Request $request)
    {
        $data = YourModel::all();
        if ($data->isEmpty()) {
            return response()->json([
                'status' => false,
                'code' => 404,
                'message' => 'No data found.',
                'data' => [],
            ], 404);
        }

        return response()->json([
            'status' => true,
            'code' => 200,
            'message' => 'Data retrieved successfully!',
            'data' => $data,
        ], 200);
    }

    public function deleteData($id)
    {
        $data = YourModel::find($id);
        if (!$data) {
            return response()->json([
                'status' => false,
                'code' => 404,
                'message' => 'Data not found.',
            ], 404);
        }
        $data->delete();

        return response()->json([
            'status' => true,
            'code' => 200,
            'message' => 'Data deleted successfully!',
        ], 200);
    }

    public function checkDuplicate(Request $request)
    {
        $exists = YourModel::where('email', $request->input('email'))->exists();
        if ($exists) {
            return response()->json([
                'status' => false,
                'code' => 409,
                'message' => 'Duplicate data found.',
            ], 409);
        }

        return response()->json([
            'status' => true,
            'code' => 200,
            'message' => 'No duplicate data found.',
        ], 200);
    }
}


/* 
    * Status code *
    Insert : 201
    Get, Update, Delete : 200
    Not found data : 404
    Check duplicate : 409
    Server error : 500
*/


Code style 

Camel Case (อูฐไหมล่ะ) 🐪🐫🐫

1. Function name : getShopProduct()
2. Variable : $shopProductID
3. Array key : $viewShowData = ['shopProductID' => $shopProductID,'shopProductName' => $shopProductName];

Note: 
Class name, Model name จะเป็น Pascal Case


Code Style จะเป็น Camel Case (อูฐไหมล่ะ 🐪🐫🐫)

1. Function name: getShopProduct()
2. Variable: $shopProductID
3. Array key: $viewShowData = ['shopProductID' => $shopProductID, 'shopProductName' => $shopProductName];

หมายเหตุ:
* Class name จะใช้ Pascal Case เช่น S3Service , ManageShopProduct 
* Class name ที่เป็น Model จะใช้ Mixed Case with Snake Case โดยอิงชื่อตาม Database : Class iCASH_Account {}

ถ้ามีไรเสนอเพิ่มเติม หรืออยากเปลี่ยนตรงไหน แจ้งได้เลยนะครับ