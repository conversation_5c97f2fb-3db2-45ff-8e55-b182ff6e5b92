<?php

namespace App\Http\Controllers\Service;

use Illuminate\Http\Request;

class LogService
{
    /**
     * Write log error from Exception
     *
     * @param Throwable $th
     * @param string $errorId
     * @return void
     */
    public static function writeLogError($th, $errorId = null)
    {
        if(!$errorId) $errorId = (string) \Illuminate\Support\Str::uuid();

        // \T2P\Util\Util::LogServer(
        //     PROJECT_CODE, 
        //     PROJECT_CODE."_BOF", 
            // array(
            //     'error_id' => $errorId,
            //     'environment' => PROJECT_ENV,
            //     'function' => request()->route()->getActionName(),
            //     'message' => "[Exception ".PROJECT_CODE."_BOF] Message: " . $exception->getMessage(),
            //     'code' => $th->getCode(),
            //     'file' => $th->getFile(),
            //     'line' => $th->getLine(),
            //     'trace' => $th->getTraceAsString(),
        //         'url' => request()->fullUrl(),
        //         'method' => request()->method(),
        //         // 'headers' => request()->headers->all(),
        //         // 'body' => request()->all(),
        //         'user_id' => session('ADMIN_DATA')['@adminId'] ?? null,
        //         'timestamp' => now()->format('Y-m-d H:i:s'),
        //     ), "BACKOFFICE_LOG"
        // );
        // dump('$res');

        $data = [
            'errorId' => $errorId,
            'environment' => PROJECT_ENV,
            'function' => request()->route()->getActionName(),
            "level" => "ERROR",
            'message' => "[Exception ".PROJECT_CODE."_BOF] Message: " . $th->getMessage(),
            'code' => $th->getCode(),
            'file' => $th->getFile(),
            'line' => $th->getLine(),
            'trace' => $th->getTraceAsString(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'userId' => session('ADMIN_DATA')['@adminId'] ?? null,
            'timestamp' => now()->format('Y-m-d H:i:s'),
        ];
        // dump($data);

        return $errorId;
    }

    /**
     * Write log error from call API
     *
     * @param [type] $info
     * @return void
     */
    public static function writeLogErrorFromApi($curlError, $curlInfo, $headers, $body, $response)
    {
        // dump($errMessage);
        // dump($curlInfo);
        // dump($curlError);
        // dump($requestData);

        // \T2P\Util\Util::LogServer(
        //     PROJECT_CODE, 
        //     PROJECT_CODE."_BOF", 
        //     array(
        //         // 'error_id' => $errorId,
        //         'environment' => PROJECT_ENV,
        //         'message' => "[cURL ".PROJECT_CODE."_BOF] " . $curlError,
        //         'url' => request()->fullUrl(),
        //         'method' => request()->method(),
        //         'headers' => $headers,
        //         'body' => $body,
        //         'response' => $response,
        //         'user_id' => session('ADMIN_DATA')['@adminId'],
        //         'timestamp' => now()->format('Y-m-d H:i:s'),
        //         'curl_url' => $curlInfo['url'] ?? null,
        //         'curl_http_code' => $curlInfo['http_code'] ?? null,
        //         'curl_total_time' => $curlInfo['total_time'] ?? null,
        //         'curl_error' => $curlError,
        //     ), "BACKOFFICE_LOG"
        // );

        //* Data Test
        $data = array(
            'environment' => PROJECT_ENV,
            'message' => "[cURL ".PROJECT_CODE."_BOF] Message: " . $curlError,
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'headers' => $headers,
            'body' => $body,
            'response' => $response,
            'user_id' => session('ADMIN_DATA')['@adminId'] ?? null,
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'curl_url' => $curlInfo['url'] ?? null,
            'curl_http_code' => $curlInfo['http_code'] ?? null,
            'curl_total_time' => $curlInfo['total_time'] ?? null,
            'curl_error' => $curlError,
        );
        dump($data);
        // $data = [
        //     'error_id' => $errorId,
        //     'environment' => PROJECT_ENV,
        //     'message' => "[Exception ".PROJECT_CODE."_BOF] Message: " . $exception->getMessage(),
        //     'code' => $exception->getCode(),
        //     'file' => $exception->getFile(),
        //     'line' => $exception->getLine(),
        //     'trace' => $exception->getTraceAsString(),
        //     'url' => request()->fullUrl(),
        //     'method' => request()->method(),
        //     'user_id' => session('ADMIN_DATA')['@adminId'],
        //     'timestamp' => now()->format('Y-m-d H:i:s'),
        // ];
    }

    /**
     * Undocumented function
     *
     * @param [type] $info
     * @return void
     */
    public static function writeLogInfo($info)
    {
        
    }
}
