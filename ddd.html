[4:47 PM] yin (yin) wai oo
<!DOCTYPE html>

<html lang="en" ng-app="myApp">

<head>

  <meta charset="UTF-8">

  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <title>Restrict Input to 10 Characters</title>

  <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>

</head>

<body ng-controller="MyController">

  <input type="text" ng-model="inputValue" restrict-length="10">

  <p>{{ inputValue }}</p>
 
  <script>

    var app = angular.module('myApp', []);
 
    app.controller('MyController', function($scope) {

      $scope.inputValue = '';

    });
 
    app.directive('restrictLength', function() {

      return {

        restrict: 'A',

        link: function(scope, element, attrs) {

          element.on('input', function() {

            var maxLength = parseInt(attrs.restrictLength, 10);

            var value = element.val();

            if (value.length > maxLength) {

              scope.$apply(function() {

                scope.inputValue = value.substring(0, maxLength);

              });

            }

          });

        }

      };

    });

  </script>

</body>

</html>

 
[4:49 PM] yin (yin) wai oo
Got it! You want to ensure that when a user inputs more than 10 characters, only the first 10 characters are kept, and the rest are replaced with an empty string. Here's how you can achieve this using AngularJS with `ng-model`:
 
### 1. AngularJS Directive

You can create a custom directive to achieve this functionality. The directive will watch the model value and modify it as needed.
 
#### HTML

```html

<!DOCTYPE html>

<html lang="en" ng-app="myApp">

<head>

  <meta charset="UTF-8">

  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <title>Restrict Input to 10 Characters</title>

  <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>

</head>

<body ng-controller="MyController">

  <input type="text" ng-model="inputValue" restrict-length="10">

  <p>{{ inputValue }}</p>
 
  <script>

    var app = angular.module('myApp', []);
 
    app.controller('MyController', function($scope) {

      $scope.inputValue = '';

    });
 
    app.directive('restrictLength', function() {

      return {

        restrict: 'A',

        link: function(scope, element, attrs) {

          element.on('input', function() {

            var maxLength = parseInt(attrs.restrictLength, 10);

            var value = element.val();

            if (value.length > maxLength) {

              scope.$apply(function() {

                scope.inputValue = value.substring(0, maxLength);

              });

            }

          });

        }

      };

    });

  </script>

</body>

</html>

```
 
### Explanation

1. **AngularJS App Setup**:

   - Define an AngularJS module named `myApp`.

   - Create a controller `MyController` with a `$scope.inputValue` initialized to an empty string.
 
2. **Custom Directive (`restrictLength`)**:

   - The `restrictLength` directive listens to the `input` event on the element it's applied to.

   - It reads the maximum length from the directive's attribute (`restrict-length`).

   - When the input length exceeds the specified maximum, it updates the model value to the first 10 characters only.
 
### Using the Directive

- The `restrictLength` directive is applied to the input field, limiting its value to 10 characters.

- The input value is automatically updated in real-time as the user types, ensuring that only the first 10 characters are kept and the rest are truncated.
 
This approach ensures that the input value will always be truncated to 10 characters without using the `maxlength` attribute, providing a seamless user experience.
 