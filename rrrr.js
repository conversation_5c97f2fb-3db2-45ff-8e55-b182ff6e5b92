const { QRCanvas: QrCanvas } = qrcanvas.vue;
var app = new Vue({
	el: "#earn-point-view",
	filters: {
		dateLocale(value, lan) {
			var d = new Date(value);
			var dd = "-";
			var lanUse = 'en-GB';
			if (lan == 'th') {
				lanUse = 'th-TH';
			}
			if (d) {
				dd = d.toLocaleDateString(lanUse, { year: 'numeric', month: 'short', day: '2-digit' });
			}

			return dd;
		},
		dateLocaleProfile(value, lan) {
			var d = new Date(value);
			var dd = "-";
			var tt = "";
			var lanUse = 'en-GB';
			if (lan == 'th') {
				lanUse = 'th-TH';
			}
			if (d) {
				dd = d.toLocaleDateString(lanUse, { year: 'numeric', month: '2-digit', day: '2-digit' });
				tt = d.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' });
			}

			return dd + ' ' + tt;
		},
	},
	components: {
		QrCanvas
	},
	data: {
		isLoaded_profile: false,
		profileLoaded: false,
		pageMode: "normal",

		qrLoading: false,
		timer: null,
		countdownMin: 0,
		countdownSec: 0,
		canUseQR: true,
		qrText: "",
		refCode: "",

		cardValue: {
			CardNumber: '',
			CardStatus: "",
			ExpireDate: "",
		},
		urlPath: "",
		errMsg: "",
		hasErr: false,
		qrData: {},
		isLoaded: false,
		modal: {},
		mode: 'qr',
		name: "",
		account: {},
		balance: 0,
		serverTime: "",
		initparam: {},
	},
	methods: {
		// lineLiffInit() {
		// 	var self = this;
		// 	// console.log(self.initparam.liffID)
		// 	liff.init({ liffId: self.initparam.liffID})
			
		// 	liff.ready.then(() => {
		// 		// check user is in line liff browser
		// 		// if (liff.isInClient()) {
		// 			if (liff.isLoggedIn()) {
					
		// 				const accessToken = liff.getAccessToken();
		// 				if (accessToken) {
		// 				  // call to init session api
		// 				  localStorage.setItem("ACCESS_TOKEN", accessToken);
						  
		// 				  self.initSession();
		// 				}

		// 			} else {
		// 				liff.login();
		// 			}
		// 		// } else {
		// 		// 	window.location.href = "unauthorize";
		// 		// }
		// 	});
		// },
		// initSession() {
		// 	var self = this;
		// 	var data = {
		// 		'accessToken' : localStorage.getItem("ACCESS_TOKEN") ? localStorage.getItem("ACCESS_TOKEN") : ""
		// 	};
		// 	axios.post(base_url_local+'/api/init-jwt-session', data).then((response) => {
		// 		var data = response.data;
		// 		console.log(data);
		// 		if(data.code != 1){
		// 				//toastr.error(data.message);
		// 				liff.closeWindow();
		// 		} else {
		// 			if(data.data.groupToken && data.data.jwtToken) {
		// 				self.hasSession = 1;
		// 				self.checkUpdateTerms();
		// 			} else {
		// 				location.href = base_url_local+"/logout";
		// 			}
		// 		}
		// 	})
		// 	.catch((error) => {
		// 		toastr.error(error);
		// 		liff.closeWindow();
		// 	});
		// },
		// checkUpdateTerms() {
		// 	self = this;
		//   	axios.post(base_url_local+'/api/check-agreement').then(function (response) {
		// 		var data = response.data;
		// 		if(data.code == 1) {
		// 			if(data.data && data.data.isRenewPolicy || data.data.isRenewTerms) {
		// 				var type = 'term';
		// 				if(data.data.isRenewTerms) {
		// 					type = 'term';
		// 				} else {
		// 					type = 'policy';
		// 				}

		// 				location.href = base_url_local+"/update-agreement?type="+type;
		// 			} else {
		// 				self.getProfile();
		// 			}
		// 		} else {
		// 			self.getProfile();
		// 		}
		// 	}).catch((error) => {
		// 		self.getProfile();
		// 	});
		// },
		goPointDetail() {
			window.location.href = base_url_local + "/point/detail";
		},
		changePageMode(mode) {
			if (mode == 'qr') {
				this.mode = "qr";
			}
			else {
				this.mode = "bar";
			}
		},
		showConfirmModal() {
			$("#confirmModal").modal('show');
		},
		loadQR() {
			$(".loader").fadeIn();
			this.canUseQR = true;

			// this.countdownMin = 15;
			//this.countdownSec = 60;
			clearInterval(this.timer);
			this.getQRData();

		},
		countDown() {
			clearInterval(this.timer)
			//  this.countdownMin = 15 //this.initparam.time
			// this.countdownSec = 60
		},
		countDownQR: function () {
			$('.qr-div canvas').show();
			var first = true;
			if (this.countdownMin > 0) {
				// var first = true
				this.timer = setInterval(() => {
					if (this.countdownSec <= 0 && this.countdownMin > 0) {
						--this.countdownMin;
						this.countdownSec = 60;
					}


					if (this.countdownSec <= 0 && this.countdownMin <= 0) {
						clearInterval(this.timer)
						//qr die
					}

					if (this.countdownSec > 0) {
						this.countdownSec--
					}

					if (this.countdownMin == 0 && this.countdownSec == 0) {
						this.canUseQR = false;
					}

				}, 1000);
			}

		},
		initBar() {
			JsBarcode("#barcode").init();
			JsBarcode("#barcode", this.cardValue.CardNumber, {
				format: "CODE128",
				lineColor: "#000",
				width: 2,
				height: 50,
				displayValue: false
			});
		},
		getProfile() {
			self = this;
			self.profileLoaded = false;
			axios.post(base_url_local + '/api/get-profile')
				.then(function (response) {
					var data = response.data;console(data);
					if (data.account) {
						self.account = data.account;
						self.balance = data.credits[0].creditAmount;
						self.pointReserve = data.pointReserve;
						self.pointReservDate = data.pointReservDate;
						self.credits = data.credits;
						self.profileLoaded = true;
					} else {


					}

				})
				.catch(function (error) {
					//location.href = "/logout";
				});
		},
		getQRData() {
			self = this;
			self.isLoaded = false;

			axios.post(base_url_local + '/api/get-qr')
				.then(response => {
					var data = response.data;
					if (data.code == 1) {
						self.qrData = data.data;

						self.qrText = self.qrData.qrcode;
						self.refCode = self.qrData.refCode;
						var expT = new Date(self.qrData.qrcodeExpiryDate);
						var serT = new Date(self.qrData.serverTime);

						var countTime = Math.abs(expT - serT);

						var minutes = Math.floor(countTime / 60000);
						var seconds = ((countTime % 60000) / 1000).toFixed(0);

						clearInterval(self.timer);

						self.countdownMin = minutes;
						self.countdownSec = seconds;
						self.canUseQR = true;
						self.isLoaded = true;
						$(".loader").fadeOut();

						self.countDownQR();

						setTimeout(function () {
							JsBarcode("#barcode").init();
							JsBarcode("#barcode", self.qrText, {
								fontSize: 12,
								format: "CODE128",
								lineColor: "#000",
								width: 2,
								height: 120,
								displayValue: true
							});
						}, 500);

					} else {
						self.errMsg = data.message;
						self.isLoaded = true;
						self.hasErr = true;
						$(".loader").fadeOut();
						toastr.error(self.errMsg);

						setTimeout(function () {
							location.href = "/logout";
						}, 1000);
					}

				})
				.catch(error => {
					self.errMsg = error;
					self.isLoaded = true;
					self.hasErr = true;
					toastr.error(self.errMsg);

					setTimeout(function () {
						location.href = "/logout";
					}, 1000);
				});


		}
	},
	computed: {
		options() {
			return {
				cellSize: 8,
				data: this.qrText,
				logo: {

				}
			};
		},
	},
	mounted() {
		this.getQRData();
		this.getProfile();
		// this.getProfile();
		// if(this.initparam.isLiff) {
		// 	this.lineLiffInit();
		// 	console.log('isLiff');
		// } else {
		// 	this.hasSession = 1;
		// 	this.getProfile();
		// 	console.log('!isLiff');

		// }
		//this.initBar();
	},
});