<?php

namespace App\Exceptions;

use Throwable;
use App\Http\Controllers\Service\LogService;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler
{
    public function render($request, Throwable $th)
    {
        $errorId = (string) \Illuminate\Support\Str::uuid();
        LogService::writeLogError($th, $errorId);

        return response()->json([
            'status' => false,
            'code' => 500,
            'message' => 'Something went wrong. Please try again.',
            'error_id' => $errorId,
        ], 500);
    }
}
