
Project = "iCRM-Share"
AdminMenuName = "Customer Churn Report"
AdminMenuDescription = "Customer Churn Report"
AdminMenuStatus = "Active"
AdminMenuType = "InternalLink"
AdminMenuLink = "downloadcenter/churnreport"
AdminMenuSortNumber = 3
AdminMenuName Master = ""
AdminMenuGroup = "Custom"
AdminMenuIconName = ""

========================================== 

Request New Admin PermissionCodeName

Project = "iCRM-Share"
AdminMenuName = "Customer Churn Report"
PermissionCodeName = "CUSTOMER_CHURN_REPORT_VIEW"

Project = "iCRM-Share"
AdminMenuName = "Customer Churn Report"
PermissionCodeName = "CUSTOMER_CHURN_REPORT_MANAGE"

==========================================

Request New Admin Role

Project = "iCRM-Share"
PermissionCodeName = "CUSTOMER_CHURN_REPORT_VIEW"
RoleName = "Download Center - Customer Churn Report (View)"
RoleTypeCreate = "Master"
RoleDescription = "Download Center - Customer Churn Report (View)"

Project = "iCRM-Share"
PermissionCodeName = "CUSTOMER_CHURN_REPORT_MANAGE"
RoleName = "Download Center - Customer Churn Report (Manage)"
RoleTypeCreate = "Master"
RoleDescription = "Download Center - Customer Churn Report (Manage)"