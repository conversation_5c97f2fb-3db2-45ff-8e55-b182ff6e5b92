<?php

public function getProductList(Request $request, $id)
	{
		if($this->canAccess || $this->canUpdate) {
			$viewShowData = "";
			$d = json_decode(file_get_contents('php://input'));

			$tableName = "iPRO_ShopProduct";
			$tableName2 = "iPRO_Coupon";
			$shopID = $id;

			$params = ["shopid" => $id];
			// $qry = "";
			$qry = "SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`,GROUP_CONCAT(DISTINCT C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode,GROUP_CONCAT(DISTINCT SPG.ShopProductGroupName) AS ShopProductGroupName";
			// $joins = "FROM `iPRO_ShopProduct` SP INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID";
			$joins = " FROM `iPRO_ShopProduct` SP
			INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` 
			LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID
			LEFT JOIN `iPRO_ShopProduct_ProductGroup` PGP ON PGP.`ShopProductID` = SP.`ShopProductID` 
			LEFT JOIN `iPRO_ShopProductGroup` SPG ON SPG.`ShopProductGroupID` = PGP.`ShopProductGroupID`";

			$orderby = " ORDER BY SP.OrderingNo ASC";

			$condition = " WHERE SP.`ShopProductStatus` <> 'Archive' AND FIND_IN_SET(C.ShopCategoryID, SP.Categories) AND SP.`ShopID` = :shopid";
			$groupby = " GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SP.`OrderingNo`, CT.CreditCode";

			$getAll = true;
			if(isset($d)){
				if(!empty($d->ShopProductStatus) && $d->ShopProductStatus != "all") {				
					$getAll = false;//dump($d->ShopProductStatus);
					$condition = $condition." AND SP.`ShopProductStatus` = :shopstatus";
					$params["shopstatus"] = $d->ShopProductStatus;
				}
				// $query = $qry.''.$joins.''.$condition.''.$groupby.''.$orderby;dd($query);
				if(!empty($d->ActiveDateRange) && !empty($d->DateActiveStart)) {
					$getAll = false;
					$aStartDate = date($d->DateActiveStart);
					$condition = $condition." AND SP.`DateActiveStart` >= :datestart";
					$params["datestart"] = $aStartDate;
				}
				if(!empty($d->ActiveDateRange) && !empty($d->DateActiveEnd)) {
					$getAll = false;
					$aEndDate = date($d->DateActiveEnd);
					$condition = $condition." AND SP.`DateActiveStart` <= :dateend";
					$params["dateend"] = $aEndDate;
				}

				if(!empty($d->Categories)) {
					
					$getAll = false;
					$catID = $d->Categories->ShopCategoryID;
					$qry .= ", SC.`OrderingNo`";
					// $qry = "SELECT SP.`ShopProductID`,SP.`ShopID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`,SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`,SP.`CouponBucketID`, SC.`OrderingNo`,GROUP_CONCAT(C.ShopCategoryName) AS ShopCategoryName, CT.CreditCode FROM `iPRO_ShopProduct_Category` SC INNER JOIN `iPRO_ShopProduct` SP ON SC.`ShopProductID` = SP.`ShopProductID` INNER JOIN `iPRO_ShopCategory` C ON C.`ShopID` = SP.`ShopID` LEFT JOIN `iCASH_Credit_Setting` CT ON CT.CreditID = SP.CreditSettingID";
					$joins .= " INNER JOIN `iPRO_ShopProduct_Category` SC ON SC.`ShopProductID` = SP.`ShopProductID`";
					$condition .= " AND SC.`ShopCategoryID` = :shopcatid";
					$params["shopcatid"] = $catID;

					$groupby = " GROUP BY SP.`ShopProductID`,SP.`ShopProductName`,SP.`ShopProductCode`,SP.`DateActiveStart`,SP.`DateActiveEnd`,SP.`ShopProductStatus`,SP.`ShopProductType`, SP.`ShopProductAge`,SP.`ShopProductUnitPrice`,SP.`ShopProductTitle`,SP.`OrderingNo`,SP.`IsUseImageThumbnail`,SP.`ImageThumbnailURL`,SP.`IsUseImagesDetail`,SP.`ImagesDetailURL`, SC.`OrderingNo`, CT.CreditCode";

					$orderby = " ORDER BY SC.OrderingNo ASC";
				}

				if (!empty($d->ProductGroups)) {
					$getAll = false;
					$proGroupID = $d->ProductGroups->ShopProductGroupID;
					// $joins .= " LEFT JOIN `iPRO_ShopProduct_ProductGroup` PGP ON PGP.`ShopProductID` = SP.`ShopProductID` 
					// 			LEFT JOIN `iPRO_ShopProductGroup` SPG ON SPG.`ShopProductGroupID` = PGP.`ShopProductGroupID`";
	
					$condition .=" AND SPG.`ShopProductGroupID` = :productgroupid";
					$params["productgroupid"] = $proGroupID;
				}
				// $query = $qry.''.$joins.''.$condition.''.$groupby.''.$orderby;
				// dd($query); 
				if(isset($d->OrderBy)) {
					$getAll = false;
					if($d->OrderBy == "adate_desc"){
						$orderby = " ORDER BY SP.`DateActiveStart` DESC ";
					}
					if($d->OrderBy == "adate_asc"){
						$orderby = " ORDER BY SP.`DateActiveStart` ASC";
					}
					if($d->OrderBy == "mobile_order" && empty($d->Categories)){
						$orderby = " ORDER BY SP.`OrderingNo` ASC";
					}
				}
			}
			if($getAll) {
				$viewShowData = DB::connection('mysql')
							->table($tableName)
							->leftJoin('iCASH_Credit_Setting', 'iCASH_Credit_Setting.CreditID', '=', 'iPRO_ShopProduct.CreditSettingID')
							->join('iPRO_ShopCategory', 'iPRO_ShopCategory.ShopID', '=', 'iPRO_ShopProduct.ShopID')
							->leftJoin('iPRO_ShopProduct_ProductGroup', 'iPRO_ShopProduct_ProductGroup.ShopProductID', '=', 'iPRO_ShopProduct.ShopProductID')
							->leftJoin('iPRO_ShopProductGroup', 'iPRO_ShopProductGroup.ShopProductGroupID', '=', 'iPRO_ShopProduct_ProductGroup.ShopProductGroupID')
							->where('iPRO_ShopProduct.ShopID', $shopID)
							->whereRaw("FIND_IN_SET(iPRO_ShopCategory.ShopCategoryID, iPRO_ShopProduct.Categories)")
							->selectRaw('GROUP_CONCAT(DISTINCT iPRO_ShopCategory.ShopCategoryName) as ShopCategoryName,iPRO_ShopProductGroup.ShopProductGroupID,iPRO_ShopProduct.*,iCASH_Credit_Setting.CreditCode')
							->groupBy('iPRO_ShopProduct.ShopProductID', 'iPRO_ShopProduct.ShopProductCode','iPRO_ShopProduct.ShopProductName')
							->orderBy('OrderingNo')
							->get();
				
				$idList = [];
				foreach ($viewShowData as $key => $value) {
					$data = DB::table('iPRO_ShopProductGroup')->where("ShopProductGroupID",$value->ShopProductGroupID)->selectRaw('GROUP_CONCAT(iPRO_ShopProductGroup.ShopProductGroupName) as ShopProductGroupName')->first();
					$value->ShopProductGroupName = null;
					if($data->ShopProductGroupName){
						$value->ShopProductGroupName = $data->ShopProductGroupName;
					}
				}

			} else {
				$query = $qry.''.$joins.''.$condition.''.$groupby.''.$orderby;
				$viewShowData = DB::select(DB::raw($query), $params); 
			}
			
			
			if(count($viewShowData) > 0) {
				$helper = new XssHelper();
				$lans = Config::get('app.languages');
				$emptyTest = [];
				if(isset($lans)) {
					foreach ($lans as $lan) {
						$emptyTest[strtoupper($lan)] = "";
					}
				}

				$lan = strtoupper($this->lan);
				foreach ($viewShowData as $key => $product) {

					$product->couponAmount = null;
					$product->ShopProductName = html_entity_decode($product->ShopProductName);
					
					if(isset($product->ShopCategoryName)) {
						//bring out empty if format in db is wrong
						$check = $this->json_validate($product->ShopCategoryName);
						if(!$check) {
							$product->ShopCategoryName = $emptyTest;
						} else {
							$product->ShopCategoryName = strip_tags(html_entity_decode($helper->cleanInputForHTML($product->ShopCategoryName)));
						}
					}
				
					$product->hasThumbnailImage = false;
					$product->hasDetailImage = false;

					if(isset($product->ImageThumbnailURL)) {
						$thumURL = (array) json_decode($product->ImageThumbnailURL);
						$product->ThumbnailUrl = isset($thumURL[$lan])? $thumURL[$lan]: "";
						$product->hasThumbnailImage = true;
					} 

					if(isset($product->ImagesDetailURL)) {
						$detailURL = (array) json_decode($product->ImagesDetailURL);
						$product->DetailUrl = isset($detailURL[$lan]) ?  $detailURL[$lan]: "";
						$product->hasDetailImage = true;
					} 
				}
				
			}

			return $viewShowData;
		} else {
			return response('Access Denied.', 403);
		}
	}