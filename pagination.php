<?php

public function getProductList(Request $request, $id)
{
	if (!($this->canAccess || $this->canUpdate)) {
		return response('Access Denied.', 403);
	}

	$start  = $request->input('start');
	$length = $request->input('length', 10);
	$page   = isset($start) ? ($start / $length) + 1 : 1;
	$draw   = $request->input('draw');
	$search = trim($request->input('search.value'));
	$order  = $request->input('order', []);

	// คอลัมน์ที่จะให้ DataTables สั่ง sort ได้
	$columns = [
		// 0 => 'iPRO_ShopProduct.ShopProductID',
		1 => 'iPRO_ShopProduct.OrderingNo',
		// 2 => 'iPRO_ShopProduct.ShopProductCode',
		3 => 'iPRO_ShopProduct.ShopProductID',
		4 => 'iPRO_ShopProduct.ShopProductCode',
		// 5 => 'iPRO_ShopProduct.ShopProductUnitPrice',
		6 => 'iPRO_ShopProduct_Category.ShopCategoryID',
		7 => 'iPRO_ShopProduct.DateActiveEnd',
		8 => 'iPRO_ShopProduct.ShopProductUnitPrice',
		9 => 'iCASH_Credit_Setting.CreditCode'
	];

	$lan = strtoupper($this->lan);
	$helper = new XssHelper();
	$emptyLangs = [];
	$lans = Config::get('app.languages');
	if ($lans) {
		foreach ($lans as $l) {
			$emptyLangs[strtoupper($l)] = "";
		}
	}

	// Base query
	$query = DB::table('iPRO_ShopProduct')
		->leftJoin('iCASH_Credit_Setting', 'iCASH_Credit_Setting.CreditID', '=', 'iPRO_ShopProduct.CreditSettingID')
		->join('iPRO_ShopCategory', 'iPRO_ShopCategory.ShopID', '=', 'iPRO_ShopProduct.ShopID')
		->leftJoin('iPRO_ShopProduct_ProductGroup', 'iPRO_ShopProduct_ProductGroup.ShopProductID', '=', 'iPRO_ShopProduct.ShopProductID')
		->leftJoin('iPRO_ShopProductGroup', 'iPRO_ShopProductGroup.ShopProductGroupID', '=', 'iPRO_ShopProduct_ProductGroup.ShopProductGroupID')
		->where('iPRO_ShopProduct.ShopID', $id)
		->whereRaw("FIND_IN_SET(iPRO_ShopCategory.ShopCategoryID, iPRO_ShopProduct.Categories)")
		->where('iPRO_ShopProduct.ShopProductStatus', '<>', 'Archive')
		->selectRaw('
			iPRO_ShopProduct.*,
			iCASH_Credit_Setting.CreditCode,
			GROUP_CONCAT(DISTINCT iPRO_ShopCategory.ShopCategoryName) as ShopCategoryName,
			GROUP_CONCAT(DISTINCT iPRO_ShopProductGroup.ShopProductGroupName) as ShopProductGroupName
		')
		->groupBy('iPRO_ShopProduct.ShopProductID');

	// Additional filters from original code
	$d = $request->all();
	// dd($d['Categories']);
	// Filter by ShopProductStatus
	if (!empty($d['ShopProductStatus']) && $d['ShopProductStatus'] != "all") {
		$query->where('iPRO_ShopProduct.ShopProductStatus', $d['ShopProductStatus']);
	}

	// Filter by DateActiveStart
	if (!empty($d['ActiveDateRange']) && !empty($d['DateActiveStart'])) {
		$query->where('iPRO_ShopProduct.DateActiveStart', '>=', date($d['DateActiveStart']));
	}

	// Filter by DateActiveEnd
	if (!empty($d['ActiveDateRange']) && !empty($d['DateActiveEnd'])) {
		$query->where('iPRO_ShopProduct.DateActiveStart', '<=', date($d['DateActiveEnd']));
	}

	// Filter by Categories
	if (!empty($d['Categories'])) {
		$catID = is_array($d['Categories']) && isset($d['Categories']['ShopCategoryID'])
			? $d['Categories']['ShopCategoryID']
			: (is_object($d['Categories']) ? $d['Categories']->ShopCategoryID : $d['Categories']);

		$query->join('iPRO_ShopProduct_Category', 'iPRO_ShopProduct_Category.ShopProductID', '=', 'iPRO_ShopProduct.ShopProductID')
			->where('iPRO_ShopProduct_Category.ShopCategoryID', $catID);
			// ->addSelect('iPRO_ShopProduct_Category.OrderingNo ');
	}

	// Filter by ProductGroups
	if (!empty($d['ProductGroups'])) {
		$proGroupID = is_array($d['ProductGroups']) && isset($d['ProductGroups']['ShopProductGroupID'])
			? $d['ProductGroups']['ShopProductGroupID']
			: (is_object($d['ProductGroups']) ? $d['ProductGroups']->ShopProductGroupID : $d['ProductGroups']);

		$query->where('iPRO_ShopProductGroup.ShopProductGroupID', $proGroupID);
	}

	// ----- ค้นหา -----
	if (!empty($search)) {
		$query->where(function ($q) use ($search) {
			$q->where('iPRO_ShopProduct.ShopProductTitle', 'like', "%{$search}%")
			->orWhere('iPRO_ShopProduct.ShopProductCode', 'like', "%{$search}%");
		});
	}

	// ----- จัดเรียง -----
	$hasCustomOrder = false;

	// Custom OrderBy from original code
	if (!empty($d['OrderBy'])) {
		$hasCustomOrder = true;
		if ($d['OrderBy'] == "adate_desc") {
			$query->orderBy('iPRO_ShopProduct.DateActiveStart', 'desc');
		} elseif ($d['OrderBy'] == "adate_asc") {
			$query->orderBy('iPRO_ShopProduct.DateActiveStart', 'asc');
		} elseif ($d['OrderBy'] == "mobile_order" && empty($d['Categories'])) {
			$query->orderBy('iPRO_ShopProduct.OrderingNo', 'asc');
		}
	}

	// DataTables sorting
	if (count($order) > 0 && !$hasCustomOrder) {
		foreach ($order as $ord) {
			$colIdx = $ord['column'];
			$dir    = $ord['dir'];
			if (isset($columns[$colIdx])) {
				$query->orderBy($columns[$colIdx], $dir);
			}
		}
	} elseif (!$hasCustomOrder) {
		// Default ordering
		if (!empty($d['Categories'])) {
			$query->orderBy('iPRO_ShopProduct_Category.OrderingNo', 'asc');
		} else {
			$query->orderBy('iPRO_ShopProduct.OrderingNo', 'asc');
		}
	}

	// ----- ดึงข้อมูลแบบแบ่งหน้า -----
	$paginator = $query->paginate($length, ['*'], '', $page);

	// ----- ปรับแต่งข้อมูลแต่ละ record -----
	$items = $paginator->items();
	foreach ($items as $product) {
		$product->couponAmount = null;
		$product->ShopProductName = html_entity_decode($product->ShopProductName);

		// ตรวจรูปแบบหมวดหมู่
		if (isset($product->ShopCategoryName)) {
			$check = $this->json_validate($product->ShopCategoryName);
			if (!$check) {
				$product->ShopCategoryName = $emptyLangs;
			} else {
				$product->ShopCategoryName = strip_tags(
					html_entity_decode($helper->cleanInputForHTML($product->ShopCategoryName))
				);
			}
		}

		$product->hasThumbnailImage = false;
		$product->hasDetailImage    = false;

		if (isset($product->ImageThumbnailURL)) {
			$thumbURL = (array) json_decode($product->ImageThumbnailURL);
			$product->ThumbnailUrl = $thumbURL[$lan] ?? "";
			$product->hasThumbnailImage = !empty($product->ThumbnailUrl);
		}

		if (isset($product->ImagesDetailURL)) {
			$detailURL = (array) json_decode($product->ImagesDetailURL);
			$product->DetailUrl = $detailURL[$lan] ?? "";
			$product->hasDetailImage = !empty($product->DetailUrl);
		}
	}

	$jsonData = [
		'draw'            => intval($draw),
		'recordsTotal'    => $paginator->total(),
		'recordsFiltered' => $paginator->total(),
		'data'            => $items,
	];

	$logger = LoggerService::init();
	$this->grayLog($request, $jsonData, 'response', $logger);

	return response()->json($jsonData);
}