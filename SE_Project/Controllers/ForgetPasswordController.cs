﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;
using System.Net;
using System.Net.Mail;

namespace SE_Project.Controllers
{
    public class ForgetPasswordController : Controller
    {
        // GET: ForgetPassword
        public ActionResult ForgetPassword()
        {
            return View();
        }

        [HttpPost]
        public string ForgetPassword(string user_email)
        {            
            var response = "";

            using (SE_Context_1 db = new SE_Context_1())
            {
                UserInfo userInfo = new UserInfo();
                bool check_email = userInfo.CheckMailOldUser(user_email);

                ForgetPasswordInfo forgetPass = new ForgetPasswordInfo();

                if (check_email == true)
                {
                    bool chk_tokenExist = forgetPass.CheckLastTokenExist(user_email); // Check ใน ForgetPasswordInfo ว่ามี Email และ IsCheck == true หรือไม่
                    if (chk_tokenExist == true)    // CheckTokenStatus(user_email)
                    {

                        string create_timeToken = forgetPass.GetCreateTimeToken(user_email);
                        string current_time = DateTime.Now.ToShortTimeString();
                        TimeSpan duration = DateTime.Parse(current_time).Subtract(DateTime.Parse(create_timeToken));

                        if (duration.TotalMinutes > 30) // เวลาปัจจุบัน - เวลาที่สร้าง token 
                        {
                            forgetPass.SetFlagTokenExpire(user_email);

                            string token = GenerateToken(10);
                            string linkUrl = CreateLinkUrl(user_email, token);

                            forgetPass.SaveUrlToken(user_email, token, forgetPass.CreateTimeToken, forgetPass.IsCheck);
                            
                            SendMailForgetPassword(user_email, linkUrl);                            
                        }
                        else // current_time - create_time < 30
                        {
                            string token = forgetPass.GetLastToken(user_email);
                            string linkUrl = CreateLinkUrl(user_email, token);

                            SendMailForgetPassword(user_email, linkUrl);
                        }
                 
                    }
                    else
                    {
                        string token = GenerateToken(10);
                        string linkUrl = CreateLinkUrl(user_email, token);

                        forgetPass.SaveUrlToken(user_email, token, forgetPass.CreateTimeToken, forgetPass.IsCheck);
                        SendMailForgetPassword(user_email, linkUrl);
                    }

                    response = "FoundEmail";
                }
                else
                {
                    response = "NotFoundEmail";
                }
            }

            return response;
        }

        
        public string CreateLinkUrl(string email, string resetToken)
        {
            var verifyUrl = "/ResetNewPassword/ResetNewPassword?user_email=" + email + "&token=" + resetToken;
            var linkUrl = Request.Url.AbsoluteUri.Replace(Request.Url.PathAndQuery, verifyUrl);

            return linkUrl;
        }

        
        private void SendMailForgetPassword(string email,string linkUrl)
        {
            using (MailMessage mm = new MailMessage("<EMAIL>", email))
            {
                mm.Subject = "Password Reset Request";
                mm.Body = "กราบสวัสดี , <br/> You recently requested to reset your password for your account. Click the link below to reset it. " +

                            " <br/><br/><a href='" + linkUrl + "'>" + linkUrl + "</a> <br/><br/>" +
                            "If you did not request a password reset, please ignore this email or reply to let us know.<br/><br/> Thank you";
                
                mm.IsBodyHtml = true;
                SmtpClient smtp = new SmtpClient();
                smtp.Host = "smtp.gmail.com";
                smtp.EnableSsl = true;
                NetworkCredential NetworkCred = new NetworkCredential("<EMAIL>", "cbmhub2499");
                smtp.UseDefaultCredentials = true;
                smtp.Credentials = NetworkCred;
                smtp.Port = 587;
                smtp.Send(mm);

            }
        }

        public string GenerateToken(int NoOfLetters)
        {
            Random random = new Random();
            string token = null;
            
            char[] letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".ToArray();

            for (int i = 0; i < NoOfLetters; i++)
            {
                token += letters[random.Next(0, letters.Length)];
            }

            return token;
        }   
        
    }

}