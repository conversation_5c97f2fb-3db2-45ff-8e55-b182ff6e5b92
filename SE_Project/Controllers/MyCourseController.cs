﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class MyCourseController : Controller
    {
        // GET: MyCourse
        public ActionResult MyCourse()
        {
            string student_name = Session["user_Name"].ToString();
            OrderCourse ordered_course = new OrderCourse();
            List<string> order_number_list = new List<string>();
            order_number_list = ordered_course.GetOrderNumber_MyCourse(student_name);

            List<string> course_name_list = new List<string>();
            course_name_list = ordered_course.GetCourseName_MyCourse(order_number_list);

            Course course = new Course();
            List<Course> course_list = new List<Course>();
            course_list = course.GetCourseDetail(course_name_list);

            return View(course_list);
        }

    }
}