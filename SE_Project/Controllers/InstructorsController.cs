﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SE_Project.Models;
using System.Web.Mvc;
using System.Net;
using System.Net.Mail;
using System.IO;
using System.Configuration;
using System.Data.SqlClient;
using System.Data.Entity;

using System.Collections;


namespace SE_Project.Controllers
{
    public class InstructorsController : Controller
    {
        public ActionResult CreateCourse1()
        {
            return View();
        }

        public ActionResult RegisInstructors()
        {
            return View();
        }

        [HttpPost]
        //public ActionResult RegisInstructors(InstructorsInfo instructorsInfo, Education education)
        //{
        //    instructorsInfo.ins_SignTime = DateTime.Now;

        //    //instructorsInfo.ins_SignTime = DateTime.Now; 
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        //UserInfo userInfo = new UserInfo();

        //        bool check = CheckMailDuplicate(instructorsInfo.ins_Email);
        //        if (check == true)
        //        {
        //            education.ID = Guid.NewGuid().ToString();
        //            db.Education.Add(education);
        //            db.SaveChanges();

        //            instructorsInfo.ins_Code = Guid.NewGuid().ToString();
        //            instructorsInfo.edu_ID = education.ID;

        //            db.InstructorsInfo.Add(instructorsInfo);
        //            db.SaveChanges();

        //            UserInfo userInfo = new UserInfo();
        //            userInfo.user_Code = instructorsInfo.ins_Code;
        //            userInfo.user_Name = instructorsInfo.ins_Name;
        //            userInfo.user_Email = instructorsInfo.ins_Email;
        //            userInfo.user_Password = instructorsInfo.ins_Password;

        //            userInfo.userType_ID = 2; // userType = "Instructors"

        //            db.UserInfo.Add(userInfo);
        //            db.SaveChanges();
        //            //SendMailRegis(instructorsInfo.ins_Email);

        //            ViewBag.js = "SuccessMessage();";
        //        }
        //        else
        //        {
        //            ViewBag.js = "ErrorMessage();";
        //        }
        //    }
        //    return View();
        //}

        //public bool CheckMailDuplicate(string email)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        if (db.InstructorsInfo.Any(u => u.ins_Email == email))
        //        {
        //            return false;  // Email ซ้ำ
        //        }
        //        else
        //        {
        //            return true; // Email ไม่ซ้ำ
        //        }
        //    }
        //}

        public void SendMailRegis(string email)
        {
            MailAddress to = new MailAddress(email);
            MailAddress from = new MailAddress("<EMAIL>");
            MailMessage message = new MailMessage(from, to);
            message.Subject = "ยืนยันการสมัครสมาชิก";
            message.Body = "ยินดีต้อนรับสู่เว็บไซต์ JBC-Learnfast ขอบคุณค่ะ ^^";

            SmtpClient client = new SmtpClient("smtp.gmail.com", 587)
            {
                Credentials = new NetworkCredential("<EMAIL>", "jbclearnfast999"),
                EnableSsl = true
            };
            // code in brackets above needed if authentication required 
            try
            {
                client.Send(message);
            }
            catch (SmtpException ex)
            {
                throw ex;
            }
        }
        //-------------------------------------------------------------------------
        public ActionResult CreateCourse()
        {
            SE_Context_1 db = new SE_Context_1();
            var categoryList = db.Category.ToList();
            ViewBag.category_ID = new SelectList(categoryList, "ID", "category_Name");
            return View();
        }

        [HttpPost]
        public ActionResult CreateCourse(Course course, IEnumerable<Lecture> lecture, IEnumerable<HttpPostedFileBase> videofile)
        {
            SE_Context_1 db = new SE_Context_1();   //104857600

            //bool checkCourseName = CheckCourseDuplicate(course.course_Name);   
            //71476fa8-1684-4758-ac5a-912f0b63ea5a
            string ins_name = "จูนิตา อยู่เผือก";// Session["user_Name"].ToString(); 
            string user_id = GetUserID(ins_name);

            var categoryList = db.Category.ToList();
            ViewBag.category_ID = new SelectList(categoryList, "ID", "category_Name");


            ArrayList videoList = new ArrayList();
            foreach (var file in videofile)
            {
                //string filename = GeneratePrefix_VideoName(4) + "_" + file.FileName;
                string filename = file.FileName;
                string gen_prefix = GeneratePrefix_VideoName(4);
                
                file.SaveAs(Server.MapPath("~/Videofiles/" + gen_prefix + "_" + filename));

                videoList.Add(gen_prefix + "_" + filename);
                Session["videofileList"] = videoList;
            }


            //course.ID = Guid.NewGuid().ToString();
            //course.user_ID = user_id;
            //course.courseStatus_ID = 1;   // สถานะเริ่มต้น ของ Course -> ยังไม่ตรวจคอร์ส
            ////string create_date = DateTime.Now.ToString("dd MMMM, yyyy");
            //course.create_Date = DateTime.Now.ToString("dd MMMM, yyyy");// DateTime.Parse("dd MMMM, yyyy");
            //db.Course.Add(course);
            //db.SaveChanges();


            //foreach (var lecList in lecture)
            //{
            //    ArrayList video_list = Session["videofileList"] as ArrayList;
            //    // var _videofile = Session["videofileList"];
            //    lecList.videoFile_Name = videofile.ToString();

            //    lecList.ID = Guid.NewGuid().ToString();
            //    lecList.course_ID = "5605692e-9884-4208-b0cd-2cb8127dae70";// course.ID;
            //    lecList.user_ID = user_id;
            //    lecList.lectureStatus_ID = 1;

            //    //int count = 0;
            //    foreach (string vdoList in video_list)
            //    {

            //        //var result = $"{video_list}";

            //        lecList.folderPath = "/Videofiles/" + $"{vdoList}";
            //        lecList.videoFile_Name = $"{vdoList}";
            //        //break;
            //    }               
            //    //lecList.folderPath = "/Videofiles/" + GeneratePrefix_VideoName(4) + "_" + lecList.videoFile_Name.ToString();
            //    //videofile.SaveAs(Server.MapPath("~/Videofiles/"  + lecList.folderPath));                
            //    db.Lecture.Add(lecList);
            //    db.SaveChanges();
            //}

            //Abc:
            //foreach (string vdoList in video_list)
            //{

            //    foreach (var lecList in lecture)
            //    {

            //        SE_Context_1 db1 = new SE_Context_1();                    
            //        lecList.ID = Guid.NewGuid().ToString();
            //        lecList.course_ID = "5605692e-9884-4208-b0cd-2cb8127dae70";// course.ID;
            //        lecList.user_ID = user_id;
            //        lecList.lectureStatus_ID = 1;
            //        lecList.folderPath = "/Videofiles/" + vdoList;
            //        lecList.videoFile_Name = vdoList;
            //        db1.Lecture.Add(lecList);
            //        db1.SaveChanges();

            //    }
            //}
            int numOfVideoList = 0;
            foreach (var lecList in lecture)
            {
                SE_Context_1 db1 = new SE_Context_1();                               
                //ArrayList video_list = Session["videofileList"] as ArrayList;
                lecList.ID = Guid.NewGuid().ToString();
                lecList.course_ID = "5605692e-9884-4208-b0cd-2cb8127dae70";// course.ID;
                lecList.user_ID = user_id;
                lecList.lectureStatus_ID = 1;
                
                lecList.folderPath = "/Videofiles/" + videoList[numOfVideoList];
                //lecList.videoFile_Name = ""+ videoList[numOfVideoList];
                //db1.Lecture.Add(lecList);
                //db1.SaveChanges();

                numOfVideoList++;               
            }


            SaveLogCreateCourse();

            return View();
            //return v1;
        }
        public bool CheckCourseDuplicate(string course_Name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                return true;
            }
        }
        public string GetUserID(string name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var user_id = "";
                var check = db.UserInfo.SingleOrDefault(u => u.user_Name == name);
                user_id = check.user_Code;
                return user_id;
            }

        }
        public string GeneratePrefix_VideoName(int NoOfLetters)
        {
            Random random = new Random();
            string output = null;
            char[] letters = "abcdefghijklmnopqrstuvwxyz".ToArray();

            for (int i = 0; i < NoOfLetters; i++)
            {
                output += letters[random.Next(0, letters.Length)];
            }
            return output;
        }

        public void SaveLogCreateCourse()
        {
            Log log = new Log();
        }

        [HttpGet]
        public ActionResult CourseListInstructor() // รายการ Course ที่ผู้สอนสร้างไว้ 
        {
            SE_Context_1 db = new SE_Context_1();

            //string time = DateTime.Now.ToString("dd MMMM, yyyy");
            
            //string ins_name = Session[""].ToString();
            //string user_id = Get_UserID(ins_name);

            List<Course> courses = db.Course.ToList();
            List<Lecture> lectures = db.Lecture.ToList();
            List<CourseStatus> courseStatuses = db.CourseStatus.ToList();

            //List<LectureStatus> lectureStatuses = db.LectureStatuses.ToList();
            //var course_list = from crl in db.Course where crl.course_Price == 3900 select crl;
            //CourseViewModel courseViewModel = new CourseViewModel();
            var courseList = (from c in courses
                              join l in lectures on c.ID equals l.course_ID
                              join cs in courseStatuses on c.courseStatus_ID equals cs.ID
                              //join ls in lectureStatuses on l.lectureStatus_ID equals ls.ID
                              where c.user_ID == "4e3be12a-31c1-4600-9e3a-fd55ecb2691e" && c.ID== "a0c2bd5f-72de-48d6-96cd-aef4b0851bba"/*user_id*///"e3842276-619c-4477-8bea-c44b829fdafc"
                              orderby c.create_Date
                              group new { c, l, cs } by c.ID into g
                              select new CourseViewModel
                              {
                                  ID = g.First().c.ID,
                                  create_Date = g.First().c.create_Date,
                                  course_Name = g.First().c.course_Name,
                                  courseStatus_ID = g.First().cs.ID,
                                  courseStatus_Name = g.First().cs.courseStatus_Name,
                                  course_Price = g.First().c.course_Price,
                                  n1 = g.Where(x => x.l.lectureStatus_ID == 1 && x.l.course_ID == "a0c2bd5f-72de-48d6-96cd-aef4b0851bba").Sum(x => x.l.lectureStatus_ID),
                                  n2 = g.Where(x => x.l.lectureStatus_ID == 2 && x.l.course_ID == "a0c2bd5f-72de-48d6-96cd-aef4b0851bba").Sum(x => x.l.lectureStatus_ID),
                                  n3 = g.Where(x => x.l.lectureStatus_ID == 3 && x.l.course_ID == "a0c2bd5f-72de-48d6-96cd-aef4b0851bba").Sum(x => x.l.lectureStatus_ID),
                                  n4 = g.Where(x => x.l.lectureStatus_ID == 4 && x.l.course_ID == "a0c2bd5f-72de-48d6-96cd-aef4b0851bba").Sum(x => x.l.lectureStatus_ID)
                                  //n5 = g.First().l.lectureStatus_ID
                              }).ToList() ;
            
            //select Course.create_Date,Course.course_Name,CourseStatus.courseStatus_Name,Course.course_Price--,count(Lecture.lecture_Name) as NumOfLec
            //from Course
            //inner join Lecture on Lecture.course_ID = Course.ID
            //inner
            //join CourseStatus on Course.courseStatus_ID = CourseStatus.ID

            // select Course.create_Date,Course.course_Name,CourseStatus.courseStatus_Name,Course.course_Price
            //	,(select count(lecture_Name) from Lecture where course_ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba' and course_Name = 'C#.net MVC')as รวม
            //	,(select count(lectureStatus_ID) from Lecture where lectureStatus_ID = 1 and course_ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba' and course_Name = 'C#.net MVC')as ยังไม่ตรวจเล็คเชอร์
            //	,(select count(lectureStatus_ID) from Lecture where lectureStatus_ID = 4 and course_ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba' and course_Name = 'C#.net MVC')as อนุมัติเล็คเชอร์
            //	,(select count(lectureStatus_ID) from Lecture where lectureStatus_ID = 2 and course_ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba' and course_Name = 'C#.net MVC')as สั่งแก้เล็คเชอร์
            //	,(select count(lectureStatus_ID) from Lecture where lectureStatus_ID = 3 and course_ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba' and course_Name = 'C#.net MVC')as รอตรวจเล็คเชอร์
            //from Course
            //inner join CourseStatus on CourseStatus.ID = Course.courseStatus_ID
            //where user_ID = '4e3be12a-31c1-4600-9e3a-fd55ecb2691e'--and Course.ID = 'a0c2bd5f-72de-48d6-96cd-aef4b0851bba'
            return View(courseList);

        }

        [HttpGet] 
        public ActionResult CourseListInstructorDetail(string course_id,string course_name) // รายการ Lecture
        {
            SE_Context_1 db = new SE_Context_1();
            ViewBag.courseName = course_name;
            ViewBag.courseID = course_id;
            var getCourseStatus = (from c in db.Course select c).SingleOrDefault(u => u.ID == course_id);
            ViewBag.courseStatusID = getCourseStatus.courseStatus_ID;
            
            List<Lecture> lectures = db.Lecture.ToList();
            List<LectureStatus> lectureStatuses = db.LectureStatuses.ToList();
            List<Course> courses = db.Course.ToList();
            
            var lecture_list = (from l in lectures
                                join ls in lectureStatuses on l.lectureStatus_ID equals ls.ID
                                join c in courses on l.course_ID equals c.ID 
                                where l.course_ID == course_id
                                //group new { l, ls, c } by l.ID into g
                                select new LectureViewModel
                                {
                                    ID = l.ID,
                                    lecture_Name = l.lecture_Name,
                                    lectureStatus_Name = ls.lectureStatus_Name,
                                    lectureStatus_ID = ls.ID,
                                    course_Name = c.course_Name
                                    //lecture = l,
                                    //lectureStatus = ls,
                                    //course = c
                                });
            return View(lecture_list);
        }

        public void CancelCourse(string course_id)
        {
            SE_Context_1 db = new SE_Context_1();

            var update_lectureStatus = (from l in db.Lecture where l.course_ID == course_id select l).ToList();

            foreach (var update_lect in update_lectureStatus)
            {
                update_lect.lectureStatus_ID = 5;   // ยกเลิกคอร์สโดยผู้สอน               
                db.SaveChanges();
            }

            var update_courseStatus = db.Course.Find(course_id);
            update_courseStatus.courseStatus_ID = 4;  //  ยกเลิกคอร์สโดยผู้สอน
            db.Entry(update_courseStatus).State = EntityState.Modified;
            db.SaveChanges();

            SaveLog_CancelCourse();
        }

        public void SaveLog_CancelCourse()
        {
            Log log = new Log();
        }

        [HttpGet]
        public ActionResult EditLecture(string lecture_id, string course_name) // หน้าจอแก้ไข Lecture
        {
            SE_Context_1 db = new SE_Context_1();

            List<Lecture> lectures = db.Lecture.ToList();
            List<LectureStatus> lectureStatuses = db.LectureStatuses.ToList();
            List<Course> courses = db.Course.ToList();
            List<Category> categories = db.Category.ToList();

            ViewBag.courseName = course_name;       //from crl in db.Lecture where crl.ID == lecture_id select crl;

            var lecture_list = (from l in lectures
                                join ls in lectureStatuses on l.lectureStatus_ID equals ls.ID
                                join c in courses on l.course_ID equals c.ID
                                join cr in categories on c.category_ID equals cr.ID
                                where l.ID == lecture_id
                                select new LectureViewModel
                                {
                                    lecture_Name = l.lecture_Name,
                                    lecture_Description = l.lecture_Description,
                                    lectureStatus_ID = ls.ID,
                                    lectureStatus_Name = ls.lectureStatus_Name,
                                    course_ID = l.course_ID,
                                    category_Name = cr.category_Name,
                                    //videoFile_Name = l.videoFile_Name,
                                    notAppr_Reason=l.notAppr_Reason                                   
                                    //lecture = l,
                                    //lectureStatus = ls,
                                    //course = c,
                                    //category = cr
                                });
            return View(lecture_list);
        }

        [HttpPost]
        public ActionResult EditLecture(string lecture_id,string lecture_name,string lecture_description,HttpPostedFileBase videofile)
        {
            string filename = videofile.FileName;
            string gen_prefix = GeneratePrefix_VideoName(4);
            videofile.SaveAs(Server.MapPath("~/Videofiles/" + gen_prefix + "_" + filename));

            SE_Context_1 db = new SE_Context_1();

            var update_lecture = db.Lecture.Find(lecture_id);

            update_lecture.lectureStatus_ID = 3; // รอตรวจเล็คเชอร์  ## ถ้า ผู้ดูแลระบบสั่งแก้ จะปรับเป็น 2 พร้อมระบุเหตุผลที่ไม่อนุมัติ 
            update_lecture.lecture_Name = lecture_name;
            //update_lecture.videoFile_Name = gen_prefix + "_" + filename;
            update_lecture.lecture_Description = lecture_description;
            update_lecture.notAppr_Reason = "";

            db.Entry(update_lecture).State = EntityState.Modified;
            db.SaveChanges();

            SaveLog_EditLecture();
            
            //บันทึกประวัติการแก้ไข Lectrue

            return RedirectToAction("CourseListInstructor");
        }

        public void SaveLog_EditLecture()
        {
            Log log = new Log();
            //Test สร้าง log file
        }

    }
}