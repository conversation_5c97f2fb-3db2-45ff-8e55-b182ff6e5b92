﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using SE_Project.Models;
using System.Data.SqlClient;
using System.Data.Entity;

namespace SE_Project.Controllers
{
    public class IncomeSoldCourseController : Controller
    {
        // GET: IncomeSoldCourse
        public ActionResult IncomeSoldCourse()
        {
            string instructor_name = Session["user_Name"].ToString();
            List<IncomeAccountDetail> incomeAccountDetails = GetSoldCourses(instructor_name);
            List<SoldCourse> sold_course_detail_list = new List<SoldCourse>();
            if (incomeAccountDetails == null)
            {
                SoldCourse sold_course = new SoldCourse
                {
                    course_name = "",
                    course_price = 0,
                    total_sold_course = 0,
                    total_course_price = 0.0,
                    service_charge = 0.0,
                    total_income = 0.0,
                };
                sold_course_detail_list.Add(sold_course);
            }
            else
            {
                sold_course_detail_list = CreateSoldCourseData(incomeAccountDetails);
            }
            
            return View(sold_course_detail_list);
        }

        public List<IncomeAccountDetail> GetSoldCourses(string instructor_name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<IncomeAccountDetail> sold_course_list = (from i in db.IncomeAccountDetail
                                                              where i.instructor_name == instructor_name
                                                              select i).ToList();
                return sold_course_list;
            }
        }

        //--------------------------------------------------------

        public List<SoldCourse> CreateSoldCourseData(List<IncomeAccountDetail> incomeAccountDetails)
        {
            List<SoldCourse> sold_course_detail_list = new List<SoldCourse>();

            foreach (var data in incomeAccountDetails)
            {
                if (sold_course_detail_list.Any(x => x.course_name == data.course_name))
                {
                    int index = sold_course_detail_list.FindIndex(x => x.course_name == data.course_name);

                    var get_object_by_index = sold_course_detail_list.ElementAt(index);
                    get_object_by_index.total_sold_course = get_object_by_index.total_sold_course + 1;
                    get_object_by_index.total_course_price = CalculateTotalCoursePrice(get_object_by_index.course_price, get_object_by_index.total_sold_course);
                    get_object_by_index.service_charge = CalculateServiceCharge(get_object_by_index.total_course_price);
                    get_object_by_index.total_income = CalculateTotalIncome(get_object_by_index.total_course_price, get_object_by_index.service_charge);
                }
                else
                {
                    SoldCourse sold_course = new SoldCourse
                    {
                        course_name = data.course_name,
                        course_price = data.course_price,
                        total_sold_course = 1,
                        total_course_price = data.course_price,
                        service_charge = data.service_charge,
                        total_income = data.income_for_instructor,
                    };
                    sold_course_detail_list.Add(sold_course);
                }
            }

            return sold_course_detail_list;
        }

        public double CalculateTotalCoursePrice(int course_price, int total_sold_course)
        {
            return course_price * total_sold_course;
        }

        public double CalculateServiceCharge(double total_course_price)
        {
            return total_course_price * 0.10;
        }

        public double CalculateTotalIncome(double total_course_price, double service_charge)
        {
            return total_course_price - service_charge;
        }
    }
}