﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
  

    public class ApprovePaymentController : Controller
    {
        // GET: ApprovePayment
        public ActionResult ApprovePayment(string order_Number)
        {
            OrderCourse order_course = new OrderCourse();
            OrderCourseDetail order_course_detail = new OrderCourseDetail();

            ViewBag.order_course = order_course.GetOrderCourse(order_Number);

            List<OrderCourseDetail> order_course_details = order_course_detail.GetOrderCourseDetailList(order_Number);
            ViewBag.order_course_details = order_course_details;
            return View();
        }

        public JsonResult CheckAccountIsActive()
        {
            IncomeAccount income_account = new IncomeAccount();
            var query_last_income_account = income_account.GetLastIncomeAccount();

            if (query_last_income_account == null) //ถ้าผู้ใช้ไม่เคยสร้างบัญชีมาก่อนเลย ข้อมูลจะเป็น null
            {
                return Json("None");
            }
            else
            {
                return Json(query_last_income_account);
            }

        }
        
        public bool CheckExpiredAccount(string date_cutoff)
        {
            bool isExpired = false;
            var date_cutoff_DateTime = Convert.ToDateTime(date_cutoff); //แปลงจาก String เป็น DateTime เพื่อที่จะนำไปหา Date diff ได้
            DateTime now = DateTime.Now;
            TimeSpan diff = date_cutoff_DateTime - now;
            int remaining_days = diff.Days;

            if (remaining_days < 0) //ถ้าวันนี้เลยวันที่ตัดบัญชีแล้ว remaining_days จะติดลบ
            {
                isExpired = true;
            }
            else
            {
                isExpired = false;
            }
            return isExpired;
        }

        
        public bool ApprovePaymentOfOrder(string order_Number)
        {
            OrderCourse order_course = new OrderCourse();
            order_course.order_Number = order_Number;
            order_course.orderStatus_ID = 3; //Status = 3 หมายถึง ชำระเงินแล้ว
            order_course.datetime_ApprovePayment = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            order_course.examiner_Name = Session["user_Name"].ToString();
            order_course.UpdateOrderCourse_ApprovePayment(order_course);

            //Save Log อนุมัติการชำระเงิน
            Log log = new Log();
            string activityName = "Approve Payment";
            string email = Session["user_Email"].ToString();
            string userType = Session["UserType"].ToString();
            string orderNumber = order_Number;
            string orderStatus = "ชำระเงินแล้ว";
            string logTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            log.SaveLogApprovePayment(activityName, email, userType, orderNumber, orderStatus, logTime);

            return true;
        }

        public JsonResult CreateListIncomeAccountDetail(List<IncomeAccountDetail> income_account_detail)
        {
            
            //--------------------//เตรียมข้อมูลสำหรับเก็บลงใน Database "IncomeAccountDetail"-----------------
            List<IncomeAccountDetail> income_detail_list = new List<IncomeAccountDetail>();
            foreach (var data in income_account_detail)
            {
                //Add ข้อมูลแต่ละ Field เข้าไปใน List IncomeAccountDetail
                IncomeAccountDetail incomeDetail = new IncomeAccountDetail
                {
                    datetime_ApprovePayment = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"),
                    order_number = data.order_number,
                    student_name = data.student_name,
                    instructor_name = data.instructor_name,
                    course_name = data.course_name,
                    course_price = data.course_price,
                    service_charge = Calculate_ServiceCharge(data.course_price),
                    transfer_status = false,
                    account_number = data.account_number
                };
                income_detail_list.Add(incomeDetail);
            }

            //Save ข้อมูลลง Database "IncomeAccountDetail"
            IncomeAccountDetail save_incomeDetail = new IncomeAccountDetail();
            save_incomeDetail.SaveIncomeAccountDetail(income_detail_list);

            return Json("True", JsonRequestBehavior.AllowGet);
        }
        
        public double Calculate_ServiceCharge(int course_price)
        {
            double service_charge = 0.0;
            service_charge = course_price * 0.10;
            return service_charge;
        }

    }
}