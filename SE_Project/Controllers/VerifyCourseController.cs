﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
    public class VerifyCourseController : Controller
    {
        // GET: VerifyCourse
        public ActionResult VerifyCourse(string course_id , string course_name ,string instructor_name ,int courseStatus_id)
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                ViewBag.CourseName = course_name;
                ViewBag.CourseId = course_id;
                ViewBag.UserName = instructor_name;
                ViewBag.courseStatus_id = courseStatus_id;

                Lecture lecture = new Lecture();
                List<Lecture> lecture_dataList = lecture.GetLectureList(course_id);
                return View(lecture_dataList);
            }
        }

        public string ApproveLecture(string lecture_id,string course_id)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                string response = "";
               
                Lecture lecture = new Lecture();
                string ins_name = Session["user_Email"].ToString();
                lecture.SetLectureStatusApprove(lecture_id, ins_name);

                var get_lec = db.Lecture.Where(x => x.course_ID == course_id && x.lectureStatus_ID == 1).ToList();               
                int count = get_lec.Count();

                if (count == 0) // ถ้าอนุมัติ Lecture ครบแล้ว ระบบจะปรับสถานะ คอร์สเรียน เป็น อนุมัติคอร์ส
                {
                    Course course = new Course();
                    int courseStatus_id = 2; // อนุมัติคอร์ส
                    course.SetCourseStatusApprove(course_id,courseStatus_id);
                    response = "อนุมัติเล็คเชอร์สุดท้ายสำเร็จ";
                }
                else
                {
                    response = "อนุมัติเล็คเชอร์สำเร็จ";
                }

                Log log = new Log();
                //log.SaveLogApproveLecture("Approve Lecture", update_lecStatus.examiner_Name, update_lecStatus.lecture_Name, "อนุมัติเล็คเชอร์", update_lecStatus.approve_Date);                
                return response;
            }
        }

        public void CancelCourseByAdmin(string course_id)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                Course course = new Course();
                Lecture lecture = new Lecture();

                int lecStatus_id = 4;   //  ยกเลิกคอร์สโดยผู้ดูแลระบบ
                int crsStatus_id = 4;   //  ยกเลิกคอร์สโดยผู้ดูแลระบบ

                List<Lecture> lecturesList = db.Lecture.Where(x => x.course_ID == course_id).ToList();
                foreach (var lec_Item in lecturesList)
                {
                    lecture.SetLectureListStatusCancel(lec_Item.ID, course_id, lecStatus_id);
                }
                //lecture.SetLectureListStatusCancel(course_id,lecStatus_id);               
                course.SetCourseStatusCancel(course_id,crsStatus_id);

                Log log = new Log();
                log.SaveLogCancelCourse();
            }
        }

    }
}