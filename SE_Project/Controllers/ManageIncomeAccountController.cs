﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class ManageIncomeAccountController : Controller
    {
        // GET: ManageIncomeAccount
        public ActionResult ManageIncomeAccount()
        {
            IncomeAccount incomeAccount = new IncomeAccount();
            List<IncomeAccount> income_account_lists = incomeAccount.GetIncomeAccountList();

            return View(income_account_lists);
        }

        public bool CheckLastAccountStatus()
        {
            bool last_account_status = false;
            IncomeAccount income_account = new IncomeAccount();
            var query_last_income_account = income_account.GetLastIncomeAccount();
            if(query_last_income_account == null)
            {
                return last_account_status;
            }
            else
            {
                last_account_status = query_last_income_account.account_status;
            }
            
            return last_account_status;
        }

        public bool CreateIncomeAccount(string start_date, string end_date)
        {
            string acc_num = GenerateAccountNumber(); //สร้างหมายเลขบัญชี
            bool acc_status = true;
            var crt_name = Session["user_Name"].ToString();
            string create_account_date = start_date;
            string cutoff_account_date = end_date;

            //-----------------เตรียมข้อมูลสำหรับเก็บลงใน Database "IncomeAccount"-----------------
            IncomeAccount incomeAccount = new IncomeAccount();
            incomeAccount.account_number = acc_num;
            incomeAccount.account_status = acc_status;
            incomeAccount.creator_name = crt_name;
            incomeAccount.create_date = create_account_date;
            incomeAccount.cutoff_date = cutoff_account_date;

            //Save ข้อมูลลง Database "Income Account"
            IncomeAccount save_incomeAccount = new IncomeAccount();
            save_incomeAccount.SaveIncomeAccount(incomeAccount);

            return true;
        }

        public string GenerateAccountNumber()
        {
            SE_Context_1 db = new SE_Context_1();

            string prefix = "AC";

            //----------------------ปีปัจุบัน-------------------------
            string current_year = DateTime.Now.Year.ToString(); //2020
            int convert_Year = Convert.ToInt32(current_year);
            convert_Year = convert_Year + 543; //แปลง 2020 ให้เป็น 2563
            string thai_year = Convert.ToString(convert_Year);

            //--------------------ลำดับของ Order (Running Number)--------------------
            var query_data = db.IncomeAccount.ToArray().LastOrDefault();

            if (query_data == null) //กรณีที่ไม่มีข้อมูลในฐานข้อมูลเลย ก็ให้ลำดับเริ่มต้นที่ 0001
            {
                return prefix + thai_year + "0001";
            }
            else
            {
                var sequence_number = query_data.account_number.ToString();
                string temp = sequence_number.Substring(6);
                int running_number = Convert.ToInt32(temp);
                string converted = Convert.ToString(running_number + 1).PadLeft(4, '0');
                string account_number = prefix + thai_year + converted;
                return account_number;
            }
        }

        public bool CutOffIncomeAccount(string account_number)
        {
            string date_time_now = DateTime.Now.ToString("dd-MM-yyyy");
            date_time_now = date_time_now.Replace('-', '/');
           
            IncomeAccount incomeAccount = new IncomeAccount();
            incomeAccount.account_number = account_number;
            incomeAccount.cutoffer_name = Session["user_Name"].ToString();
            incomeAccount.account_status = false;
            incomeAccount.cutoff_date = date_time_now;
            incomeAccount.UpdateIncomeAccount_Cutoff(incomeAccount);

            return true;
        }

    }
}