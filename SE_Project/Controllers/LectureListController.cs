﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
    public class LectureListController : Controller
    {
        // GET: LectureList
        public ActionResult LectureList(string course_id, string course_name,int course_statusId)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                ViewBag.courseName = course_name;
                ViewBag.courseID = course_id;

                //var getCourseStatus = (from c in db.Course select c).SingleOrDefault(u => u.ID == course_id);
                ViewBag.courseStatus_id = course_statusId;//= getCourseStatus.courseStatus_ID;

                Lecture lecture = new Lecture();
                List<Lecture> lecture_list = lecture.GetLectureList(course_id);

                return View(lecture_list);
            }
            
        }

        public void CancelCourseByInstructors(string course_id)
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                Course course = new Course();
                Lecture lecture = new Lecture();

                int lecStatus_id = 3;   // ยกเลิกคอร์สโดยผู้สอน
                int crsStatus_id = 3;   //  ยกเลิกคอร์สโดยผู้สอน

                List<Lecture> lecturesList = db.Lecture.Where(x => x.course_ID == course_id).ToList();
                foreach (var lec_Item in lecturesList)
                {
                    lecture.SetLectureListStatusCancel(lec_Item.ID, course_id, lecStatus_id);
                }

                //lecture.SetLectureListStatusCancel(course_id, lecStatus_id); // ปรับสถานะ รายการ lecture ของคอร์สนั้นๆ             

                course.SetCourseStatusCancel(course_id, crsStatus_id);

                Log log = new Log();
                log.SaveLogCancelCourse();
            }            
        }
    }
}