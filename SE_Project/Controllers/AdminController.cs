﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class AdminController : Controller
    {
        //public ActionResult AdminMainPage()
        //{
        //    return View();
        //}

        public ActionResult CourseListInfo(string a,string b)
        {
            return View();    //รายการข้อมูลคอร์สเรียน
        }

        public ActionResult CourseOrderList()
        {
            return View();    // รายการใบสั่งซื้อคอร์สเรียน
        }

        public ActionResult CourseListInfoDetail()
        {
            return View();    //ตรวจสอบคอร์สเรียน   [อนุมัติ Lecture]
        }

       
        public ActionResult ApprovePayment()
        {
            return View();    //อนุมัติชำระเงิน
        }

        public ActionResult Report()
        {
            return View();     // หน้าจอ Report
        }

        public ActionResult CourseReport()
        {
            return View();     // หน้าจอ Report เกี่ยวกับคอร์สเรียน
        }

        public ActionResult InstructorReport()
        {
            return View();     // หน้าจอ Report เกี่ยวกับ ผู้สอน
        }
        public ActionResult InstructorReportDetail()
        {
            return View();   // แสดงหน้าจอ Report รายละเอียดการสร้างคอร์สเรียนของผู้สอน
        }

        public ActionResult StudentReport()
        {
            return View();   // แสดงหน้าจอ  Report เกี่ยวกับ ผู้เรียน
        }

        public ActionResult StudentReportDetail()
        {
            return View();   // แสดงหน้าจอ Report รายละเอียดการสั่งซื้อคอร์สเรียนของผู้สอน
        }


        //[HttpPost]
        public ActionResult TestCheckbox()
        {           
            return View();
        }

        [HttpPost]
        public void TestCheckbox(string[] category)
        {
            //for(int i = 0; i < category.Length; i++)
            //{
            if (category.Length == 1)
            {
                var _category = category.ToString();
                if (_category == "prg")
                {
                    Console.Write("");
                }
                if (category.ToString() == "lang")
                {
                    Console.Write("");
                }
                if (category.ToString() == "tutor")
                {
                    Console.Write("");
                }
            }
            if (category.Length == 2)
            {

            }
            if (category.Length == 3)
            {

            }
            // }    
            //string []output = "";
            //foreach(string catg in category)
            //{
            //category = new string[] { };

            //}

            //return View();
        }
    }
}

//if (catg.ToString() == "prg")
//{
//    Console.Write("");
//}
//else if (catg.ToString() == "lang")
//{
//    Console.Write("");
//}
//else if (catg.ToString() == "tutor")
//{
//    Console.Write("");
//}
//else
//{
//    Console.Write("");
//}