﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
    public class CourseDataListController : Controller
    {
        // GET: CourseDataList
        public ActionResult CourseDataList()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                Course course = new Course();
                List<Course> courseDataList = course.GetCourseDataList();
                return View(courseDataList); 
            }
        }
    }
}