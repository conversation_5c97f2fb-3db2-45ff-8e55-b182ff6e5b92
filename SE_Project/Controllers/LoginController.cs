﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class LoginController : Controller
    {
        [HttpPost]
        public string Login(string user_email,string user_password)
        {
            UserInfo userInfo = new UserInfo();
            Log log = new Log();

            using (SE_Context_1 db = new SE_Context_1())
            {
                string log_time = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
                var type = "";
                var email = "";
                var data = (from s in db.UserInfo select s).SingleOrDefault(u => u.user_Email == user_email);

                var user_type = userInfo.GetUserType(user_email, user_password);


                if(user_type == "3")
                {
                    type = "Students";
                    email = data.user_Email;
                    Session["UserType"] = "Students";
                    Session["user_Name"] = data.user_Name;
                    Session["user_Email"] = email;

                    log.SaveLog("Login", email, "Students", log_time);
                }
                else if (user_type == "2")
                {
                    type = "Instructors";
                    email = data.user_Email;
                    Session["UserType"] = "Instructors";
                    Session["user_Name"] = data.user_Name;
                    Session["user_Email"] = email;

                    log.SaveLog("Login", email, "Instructors", log_time);
                }
                else if(user_type == "1")
                {
                    type = "Admins";
                    email = data.user_Email;
                    Session["UserType"] = "Admins";
                    Session["user_Name"] = data.user_Name;
                    Session["user_Email"] = email;

                    log.SaveLog("Login", email, "Admin", log_time);
                }
                else
                {
                    type = null;
                    // ไม่พบ Email
                }
                return type;
            }
        }

        //public string GetUserType(string user_email, string user_password)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        var user_type = "";
        //        var check = db.UserInfo.SingleOrDefault(u => u.user_Email == user_email && u.user_Password == user_password);
        //        if (check == null) // ไม่พบ user type
        //        {
        //            user_type = "";
        //            return user_type;
        //        }
        //        else
        //        {
        //            user_type = check.userType_ID.ToString(); // Get User Type
        //            return user_type;
        //        }
        //    }
        //}

        [HttpGet]
        public ActionResult Logout()
        {
            
            string log_time = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            string email = Session["user_Email"].ToString();
            string user_type = Session["UserType"].ToString();

            Log log = new Log();
            log.SaveLog("Logout", email, user_type, log_time);

            Session.Clear();
            Session.Abandon();

            return RedirectToAction("Index","Home");
        }

        //public void SaveLog(string _activityName, string _email, string _userType, string _logTime)
        //{
        //    Log log = new Log();
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        //Log log = new Log();  //Save Log ลง DataBase

        //        log.ID = Guid.NewGuid().ToString();
        //        log.activityName = _activityName;
        //        log.email = _email;
        //        log.userType = _userType;
        //        log.logTime = _logTime;
        //        db.Log.Add(log);
        //        db.SaveChanges();
        //    }
        //}
    }
}