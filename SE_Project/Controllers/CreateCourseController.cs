﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;
using System.Collections;
namespace SE_Project.Controllers
{
    public class CreateCourseController : Controller
    {
        // GET: CreateCourse
        public ActionResult CreateCourse()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var categoryList = db.Category.ToList();
                ViewBag.category_ID = new SelectList(categoryList, "ID", "category_Name");
            }
            
            
            return View();
        }

        

        [HttpPost]
        public ActionResult CreateCourse(Course course, IEnumerable<Lecture> lecture)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {               
                //ArrayList lecture_ids = new ArrayList();
                //ArrayList videoList = new ArrayList();     
                bool checkCourseName = course.CheckCourseNameDuplicate(course.course_Name);

                if(checkCourseName == false)
                {
                    UserInfo userInfo = new UserInfo();
                    string ins_name = Session["user_Name"].ToString();// ; "จูนิตา อยู่เผือก";//
                    string user_id = userInfo.GetUserID(ins_name);

                    // Populate Dropdown List (Category)
                    var categoryList = db.Category.ToList();
                    ViewBag.category_ID = new SelectList(categoryList, "ID", "category_Name");

                    course.ID = Guid.NewGuid().ToString();
                    course.user_ID = user_id;
                    course.courseStatus_ID = 1;   // สถานะเริ่มต้น ของ Course -> ยังไม่ตรวจคอร์ส               
                    course.create_Date = DateTime.Now.ToString("dd MMMM yyyy");// DateTime.Parse("dd MMMM, yyyy");
                    course.instructor_Name = ins_name;
                    course.SaveCourseInfo(course);
                    
                    foreach (var lecItems in lecture)  //List of Items
                    {
                        //lecItems.videoFile_Name;
                        //SE_Context_1 db1 = new SE_Context_1();
                        //ArrayList video_list = Session["videofileList"] as ArrayList;
                        if (lecItems.lecture_Name != null && lecItems.lecture_Description != null)
                        {
                            string gen_prefix = "";
                            gen_prefix = GeneratePrefix_VideoName(4);

                            HttpPostedFileBase file = lecItems.videoFile_Name.FirstOrDefault();
                            SaveVideoFile(file, $"{gen_prefix}_{file.FileName}");
                                                                      
                            lecItems.ID = Guid.NewGuid().ToString();
                            lecItems.course_ID = course.ID; //  "62589f3a-f475-4f14-8093-34d564a3ab91";
                            lecItems.user_ID = user_id;
                            lecItems.lectureStatus_ID = 1;
                            lecItems.folderPath = "/Videofiles/" + $"{gen_prefix}_{file.FileName}";
                            //lecItems.videoFile_Name = "" +videoList[numOfVideoList];
                            lecItems.lecture_CreateDate = DateTime.Now.ToString("HH:mm:ss.fff");

                            Lecture lec = new Lecture();
                            lec.SaveLectureListInfo(lecItems);
                        }

                        //numOfVideoList++;
                    }
                    Log log = new Log();
                    log.SaveLogCreateCourse("Create Course", ins_name, course.course_Name, "ยังไม่ตรวจคอร์ส", course.create_Date);

                    ViewBag.message = "SuccessMessages();";
                }
                else
                {
                    // Populate Dropdown List (Category)
                    var categoryList = db.Category.ToList();
                    ViewBag.category_ID = new SelectList(categoryList, "ID", "category_Name");

                    ViewBag.message = "ErrorMessages();";
                }
                
            }

            return View();
        }

        private void SaveVideoFile(HttpPostedFileBase file, string file_name)
        {
            file.SaveAs(Server.MapPath($"~/Videofiles/{file_name}"));
        }

        public string GeneratePrefix_VideoName(int NoOfLetters)
        {
            Random random = new Random();
            string output = null;
            char[] letters = "abcdefghijklmnopqrstuvwxyz".ToArray();

            for (int i = 0; i < NoOfLetters; i++)
            {
                output += letters[random.Next(0, letters.Length)];
            }
            return output;
        }
    }
}