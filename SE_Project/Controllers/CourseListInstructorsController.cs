﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
    public class CourseListInstructorsController : Controller
    {
        // GET: CourseListInstructors
        public ActionResult CourseListInstructors()
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                Course course = new Course();
                string ins_name = Session["user_Name"].ToString();

                List<Course> courseList = course.GetCourseList(ins_name);
                
                return View(courseList);

            }

            //List<Course> courseList = db.Course.ToList();
            //string time = DateTime.Now.ToString("dd MMMM, yyyy");
        }

        //public List<CourseViewModel> GetCourseList()
        //{
        //    SE_Context_1 db = new SE_Context_1();

        //    string time = DateTime.Now.ToString("dd MMMM, yyyy");
        //    List<Course> courses = db.Course.ToList();
        //    List<Lecture> lectures = db.Lecture.ToList();
        //    List<CourseStatus> courseStatuses = db.CourseStatus.ToList();
            
        //    var courseList = (from c in courses
        //                      join l in lectures on c.ID equals l.course_ID
        //                      join cs in courseStatuses on c.courseStatus_ID equals cs.ID
        //                      //join ls in lectureStatuses on l.lectureStatus_ID equals ls.ID
        //                      where c.user_ID == "4e3be12a-31c1-4600-9e3a-fd55ecb2691e" && c.ID == "a0c2bd5f-72de-48d6-96cd-aef4b0851bba"/*user_id*///"e3842276-619c-4477-8bea-c44b829fdafc"
        //                      orderby c.create_Date
        //                      group new { c, l, cs } by c.ID into g
        //                      select new CourseViewModel
        //                      {
        //                          ID = g.First().c.ID,
        //                          create_Date = g.First().c.create_Date,
        //                          course_Name = g.First().c.course_Name,
        //                          courseStatus_ID = g.First().cs.ID,
        //                          courseStatus_Name = g.First().cs.courseStatus_Name,
        //                          course_Price = g.First().c.course_Price,
        //                      }).ToList();

        //    return courseList;
        //}
        //public List<Course> QueryCourseList()
        //{
        //    return ""
        //}
    }
}