﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class PaymentNotifyController : Controller
    {
        public static string static_order_Number = "";
        public static string static_user_Name = "";

        public ActionResult PaymentNotify()
        {
            OrderCourse orderCourse = new OrderCourse();
            static_user_Name = Session["user_Name"].ToString();
            ViewBag.last_order = orderCourse.GetLastOrderedCourse(static_user_Name);

            if(ViewBag.last_order != null)
            {
                static_order_Number = ViewBag.last_order.order_Number;
            }
            return View();
        }

        [HttpPost]
        public ActionResult PaymentNotify(OrderCourse payment_notify)
        {
            OrderCourse orderCourse = new OrderCourse();
            string imgfileName = GenerateFileName(payment_notify, static_order_Number); //Generate ชื่อไฟล์ภาพ
            SaveImageFile(payment_notify, imgfileName); //Save ไฟล์ภาพขึ้น Server

            //-------------------------Update ข้อมูลในฐานข้อมูล OrderCourse----------------------
            payment_notify.order_Number = static_order_Number;
            payment_notify.datetime_TransferNotify = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            payment_notify.orderStatus_ID = 2; //Status = 2 หมายถึง รอตรวจสอบชำระเงิน
            payment_notify.UpdateOrderCourse_PaymentNotify(payment_notify);

            //-------------------------Save log แจ้งชำระเงิน (Payment Notify)----------------------
            Log log = new Log();
            string activityName = "Payment Notify";
            string userEmail = Session["user_Email"].ToString();
            string userType = Session["UserType"].ToString();
            string orderNumber = static_order_Number;
            string orderStatus = "รอตรวจสอบชำระเงิน";
            string logTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            log.SaveLogPaymentNotify(activityName, userEmail, userType, orderNumber, orderStatus, logTime);
            ViewBag.showSwal = "True";
            ModelState.Clear();
            ViewBag.last_order = orderCourse.GetLastOrderedCourse(static_user_Name);
            return View();
        }
        public string GenerateFileName(OrderCourse payment_notify, string order_Number)
        {
            string imageFileName = order_Number;  //สร้างชื่อไฟล์ = หมายเลขสั่งซื้อ
            string extensionName = Path.GetExtension(payment_notify.ImageFile.FileName); //นามสกุลไฟล์
            imageFileName = imageFileName + extensionName;

            return imageFileName;
        }

        public void SaveImageFile(OrderCourse payment_notify, string fileName)
        {
            //Save ไฟล์ภาพ
            payment_notify.image_path = "~/image/" + fileName;
            fileName = Path.Combine(Server.MapPath("~/image/"), fileName); //สร้างชื่อไฟล์ภาพบน server ให้ตรงกับ path ในฐานข้อมูล
            payment_notify.ImageFile.SaveAs(fileName); //Save ไฟล์ภาพขึ้น server (folder ชื่อ image)
        }
    }
}