﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;
using System.Net;
using System.Net.Mail;

namespace SE_Project.Controllers
{
    public class StudentsController : Controller
    {
        //public ActionResult Index()
        //{
        //    return View();
        //}

        public ActionResult RegisStudents()
        {
            //Session["UserType"] = user_type;

            return View();
        }

        [HttpPost]
        //public ActionResult RegisStudents()
        //{
        //var user_type = Session["UserType"];

        //studentsInfo.stu_Code = Guid.NewGuid().ToString();
        //studentsInfo.stu_SignTime = DateTime.Now; 
        //using (SE_Context_1 db = new SE_Context_1())
        //{

        //    bool check = CheckMailDuplicate(studentsInfo.stu_Email);

        //    if(check == true)
        //    {
        //        db.StudentsInfo.Add(studentsInfo);
        //        var i = studentsInfo.stu_Code;
        //        db.SaveChanges();
        //        UserInfo userInfo = new UserInfo();                   
        //        userInfo.user_Code = i;
        //        userInfo.user_Name = studentsInfo.stu_Name;
        //        userInfo.user_Email = studentsInfo.stu_Email;
        //        userInfo.user_Password = studentsInfo.stu_Password;                   
        //        userInfo.userType_ID = 3; // userType = "Students"

        //        db.UserInfo.Add(userInfo);
        //        db.SaveChanges();
        //        //SendMailRegis(studentsInfoTemp.stu_Email);

        //        ViewBag.js = "SuccessMessage();";

        //    }
        //    else
        //    {
        //        ViewBag.js = "ErrorMessage();";
        //    }
        //}
        //    return View();
        //}

        public bool CheckMailDuplicate(string email)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                //if (db.StudentsInfo.Any(u => u.stu_Email == email))
                //{
                //    return false;  // Email ซ้ำ
                //}
                //else
                //{
                return true; // Email ไม่ซ้ำ
                //}
            }
        }

        public void SendMailRegis(string email)
        {
            MailAddress to = new MailAddress(email);
            MailAddress from = new MailAddress("<EMAIL>");
            MailMessage message = new MailMessage(from, to);
            message.Subject = "ยืนยันการสมัครสมาชิก";
            message.Body = "ยินดีต้อนรับสู่เว็บไซต์ JBC-Learnfast ขอบคุณค่ะ ^^";

            SmtpClient client = new SmtpClient("smtp.gmail.com", 587)
            {
                Credentials = new NetworkCredential("<EMAIL>", "jbclearnfast999"),
                EnableSsl = true
            };
            // code in brackets above needed if authentication required 
            try
            {
                client.Send(message);
            }
            catch (SmtpException ex)
            {
                throw ex;
            }
        }

        [HttpPost]
        public bool CheckLogin()
        {
            bool isLogin;
            if (Session["UserType"] == null)
            {
                isLogin = false;
            }
            else
            {
                isLogin = true;
            }
            return isLogin;
        }

        public int CheckLastOrderStatus()
        {
            var user_Name = Session["user_Name"].ToString(); //Get User_name จาก Session

            OrderCourse order_course = new OrderCourse();
            var query_last_order_status = order_course.GetLastOrderStatus(user_Name);

            if (query_last_order_status == null) //ถ้าผู้ใช้ไม่เคยซื้อคอร์สมาก่อนเลย ข้อมูลจะเป็น null
            {
                return 0;
                //return "";

            }
            else
            {
                return query_last_order_status.orderStatus_ID;
                // string last_order_status = Convert.ToString(query_last_order_status.order_Status);
                // return last_order_status;
            }
        }

        public JsonResult GetCourseData(List<string> course_id)
        {
            Course course = new Course();
            var courses_data = course.GetCourseData(course_id);

            return Json(courses_data, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult CreateOrderCourse(List<Course> courses_data)
        {
            string order_Number = GenerateOrderNumber(); //Generate Order Number
            var user_Name = Session["user_Name"].ToString(); //Get User_name จาก Session
            int init_order_status = 1; //สถานะเริ่มต้นของใบสั่งซื้อ กำหนดให้ = 1 หมายถึง รอชำระเงิน
            string date_time_order = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

            //-----------------เตรียมข้อมูลสำหรับเก็บลงใน Database "OrderCourse"-----------------
            OrderCourse orderCourse = new OrderCourse();
            orderCourse.order_Number = order_Number;
            orderCourse.student_Name = user_Name;
            orderCourse.total_Course = courses_data.Count; //จำนวนคอร์สที่ซื้อทั้งหมด
            orderCourse.total_Price = CalculateTotalPrice(courses_data); //ราคารวมทั้งหมด
            orderCourse.orderStatus_ID = init_order_status;
            orderCourse.datetime_OrderCourse = date_time_order;

            //Save ข้อมูลลง Database "OrderCourse"
            OrderCourse save_orderCourse = new OrderCourse();
            save_orderCourse.SaveOrderCourse(orderCourse);

            //--------------------//เตรียมข้อมูลสำหรับเก็บลงใน Database "OrderCourseDetail"-----------------
            List<OrderCourseDetail> orderCourseList = new List<OrderCourseDetail>();
            foreach (var data in courses_data)
            {
                //Add ข้อมูลแต่ละ Field เข้าไปใน List orderCourseDetails
                OrderCourseDetail orderCourseDetail = new OrderCourseDetail
                {
                    instructor_Name = data.instructor_Name,
                    course_Name = data.course_Name,
                    course_Price = data.course_Price,
                    order_Number = order_Number
                };
                orderCourseList.Add(orderCourseDetail);
            }

            //Save ข้อมูลลง Database "OrderCourseDetail"
            OrderCourseDetail save_orderCourseDetail = new OrderCourseDetail();
            save_orderCourseDetail.SaveOrderCourseDetail(orderCourseList);

            //-------------------------Save log ซื้อคอร์ส (orderCourse)----------------------
            Log log = new Log();
            string activityName = "Order Course";
            string email = Session["user_Email"].ToString();
            string userType = Session["UserType"].ToString();
            string orderNumber = order_Number;
            string orderStatus = "รอชำระเงิน";
            string logTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            log.SaveLogOrderCourse(activityName, email, userType, orderNumber, orderStatus, logTime);

            return Json(order_Number, JsonRequestBehavior.AllowGet);
        }

        public string GenerateOrderNumber()
        {
            SE_Context_1 db = new SE_Context_1();

            string prefix = "OD";

            //----------------------ปีปัจุบัน-------------------------
            string current_year = DateTime.Now.Year.ToString(); //2020
            int convert_Year = Convert.ToInt32(current_year);
            convert_Year = convert_Year + 543; //แปลง 2020 ให้เป็น 2563
            string thai_year = Convert.ToString(convert_Year);

            //--------------------ลำดับของ Order (Running Number)--------------------
            var query_data = db.OrderCourse.ToArray().LastOrDefault();

            if (query_data == null) //กรณีที่ไม่มีข้อมูลในฐานข้อมูลเลย ก็ให้ลำดับเริ่มต้นที่ 0001
            {
                return prefix + thai_year + "0001";
            }
            else
            {
                var sequence_number = query_data.order_Number.ToString();
                string temp = sequence_number.Substring(6);
                int running_number = Convert.ToInt32(temp);
                string converted = Convert.ToString(running_number + 1).PadLeft(4, '0');
                string order_Number = prefix + thai_year + converted;
                return order_Number;
            }

        }

        public int CalculateTotalPrice(List<Course> courses_data)
        {
            int total_price = 0;

            //หาผลรวมของราคาคอร์สทั้งหมด
            foreach (var data in courses_data)
            {
                total_price = total_price + data.course_Price;
            }

            return total_price;
        }


        public ActionResult AllCourse() // คอร์สเรียนทั้งหมด
        {
            return View();
        }
        public ActionResult MyCourse() // คอร์สเรียนของฉัน
        {
            return View();
        }
        public ActionResult PaymentNotification() //แจ้งชำระเงิน
        {
            return View();
        }
    }
}