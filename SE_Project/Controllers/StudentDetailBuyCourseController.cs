﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using SE_Project.Models;
using System.Data.SqlClient;
using System.Data.Entity;

namespace SE_Project.Controllers
{
    public class StudentDetailBuyCourseController : Controller
    {
        // GET: StudentDetailBuyCourse
        public ActionResult StudentDetailBuyCourse(string course_name)
        {
            IncomeAccountDetail income_data = new IncomeAccountDetail();
            List<string> order_number = income_data.GetOrderNumber(course_name);

            DetailOfStudentByCourse student_detail = new DetailOfStudentByCourse();
            List<DetailOfStudentByCourse> student_detail_lists = new List<DetailOfStudentByCourse>();
            student_detail_lists = student_detail.GetDetailOfStudentByCoursesList(order_number);
            ViewBag.course_name = course_name;
            return View(student_detail_lists);
        }

    }
}