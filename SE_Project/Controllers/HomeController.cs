﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using SE_Project.Models;
using System.Data.SqlClient;
using System.Net.Mail;
using HtmlAgilityPack;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Globalization;
using System.Threading;

namespace SE_Project.Controllers
{
    public class HomeController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            CourseListModel course = new CourseListModel();

            using (SE_Context_1 db = new SE_Context_1())
            {
                course.Courses = (from c in db.Course
                                  where c.courseStatus_ID == 2
                                  select c).ToList();

            }
            return View(course);   // หน้ารายการ คอร์สเรียน   หรือ  หน้าหลัก สำหรับผู้เรียน
        }

        public ActionResult AdminMainPage()
        {
            return View();  // หน้าจอหลัก สำหรับ Admin
        }

        public ActionResult InstructorMainPage()
        {
            //if (Session["UserType"] != null) 
            //{
            //   if (Session["UserType"].ToString() == "Instructors")
            //   {
            return View();   // หน้าจอหลัก สำหรับ ผู้สอน

            //  }                             
            //}
            //return RedirectToAction("Index");
        }

        public ActionResult StudentMainPage()
        {
            CourseListModel course = new CourseListModel();

            using (SE_Context_1 db = new SE_Context_1())
            {
                course.Courses = (from c in db.Course
                                  where c.courseStatus_ID == 2
                                  select c).ToList();

            }
            return View(course);   // หน้ารายการ คอร์สเรียน   หรือ  หน้าหลัก สำหรับผู้เรียน
        }

        [HttpPost]
        public bool CheckLogin()
        {
            bool isLogin;
            if (Session["UserType"] == null)
            {
                isLogin = false;
            }
            else
            {
                isLogin = true;
            }
            return isLogin;
        }
    }
}