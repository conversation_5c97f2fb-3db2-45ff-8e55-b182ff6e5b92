﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Diagnostics;
//using System.Data.Entity.Validation;

namespace SE_Project.Controllers
{
    public class ResetNewPasswordController : Controller
    {
        public ActionResult ResetNewPassword(string token , string user_email)
        {

            using (SE_Context_1 db = new SE_Context_1())
            {
                ForgetPasswordInfo forgetPassword = new ForgetPasswordInfo();

                bool last_token = forgetPassword.CheckLastToken(token);

                if (last_token == true)
                {
                    string create_time = forgetPassword.GetCreateTimeToken(user_email);
                    string current_time = DateTime.Now.ToShortTimeString();
                    TimeSpan duration = DateTime.Parse(current_time).Subtract(DateTime.Parse(create_time));

                    if (duration.TotalMinutes > 30) // เวลาปัจจุบัน - เวลาที่สร้าง token
                    {
                        forgetPassword.SetFlagTokenExpire(user_email);  // Set flag ให้เป็น false
                        return RedirectToAction("TokenExpire");  // กรณี Link URL เกิน 30 นาที จะแสดงหน้าจอ แสดงข้อความ "Link URL หมดอายุ"
                    }
                    else   // current_time - create_time < 30
                    {
                        ResetPasswordModel model = new ResetPasswordModel();
                        model.email = user_email;
                        return View(model); //กรณี Link URL ยังไม่หมดอายุ จะแสดงหน้าจอ เปลี่ยนรหัสผ่านใหม่
                    }

                }
                else
                {
                    return HttpNotFound();
                    // ลิงค์ ไม่สามารถใช้งานได้     กรณี เปิด Link ก่อนหน้า ที่หมดอายุไปแล้ว

                }               
            }           
        }

        [HttpPost]
        public string ResetNewPasswords(string user_email , string new_password)
        {
            string response = "";
            UserInfo userInfo = new UserInfo();
            ForgetPasswordInfo forgetPassword = new ForgetPasswordInfo();
            Log log = new Log();

            forgetPassword.SetFlagTokenExpire(user_email);
            userInfo.SaveChangePassword(user_email,new_password);
            //log.SaveLogChangePassword();

            return response;
        }
       
        public ActionResult TokenExpire()
        {
            return View();    // แสดงหน้าจอ Link หมดอายุ
        }
     
    }
}