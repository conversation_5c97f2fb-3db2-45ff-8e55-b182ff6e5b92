﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Net;
using System.Net.Mail;

namespace SE_Project.Controllers
{
    public class TransferIncomeNotifyController : Controller
    {
        // GET: TransferIncomeNotify
        public ActionResult TransferIncomeNotify(string order_Number, string course_Name)
        {
            IncomeAccountDetail income_for_instructor_detail = new IncomeAccountDetail();
            income_for_instructor_detail = income_for_instructor_detail.GetIncomeDetail(order_Number, course_Name);
            string instructor_name = income_for_instructor_detail.instructor_name;

            UserInfo account_of_user = new UserInfo();
            account_of_user = account_of_user.GetAccountDetail(instructor_name);
            //----------------------------------------
            ViewBag.account_number = income_for_instructor_detail.account_number;
            ViewBag.order_number = income_for_instructor_detail.order_number;
            ViewBag.course_name = income_for_instructor_detail.course_name;
            ViewBag.instructor_name = instructor_name;
            ViewBag.accout_number_of_instructor = account_of_user.user_AccountNumber;
            ViewBag.bank_name_of_instructor = account_of_user.user_BankName;
            ViewBag.transfer_status = income_for_instructor_detail.transfer_status;
            ViewBag.income_for_instructor = income_for_instructor_detail.income_for_instructor;
            return View();
        }

        public bool TransferIncomeToInstructor(string order_number, string course_name)
        {
            string transferer = Session["user_Name"].ToString();
            bool transfer_status = true;
            string datetime_transfer = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

            IncomeAccountDetail income_for_instructor_detail = new IncomeAccountDetail();
            income_for_instructor_detail.order_number = order_number;
            income_for_instructor_detail.course_name = course_name;
            income_for_instructor_detail.transferer = transferer;
            income_for_instructor_detail.transfer_status = transfer_status;
            income_for_instructor_detail.datetime_transfer = datetime_transfer;

            income_for_instructor_detail.SaveTransferringIncomeToInstructor(income_for_instructor_detail);
            SentMailForIncomeNotify(order_number, course_name);

            return true;
        }

        public void SentMailForIncomeNotify(string order_number, string course_name)
        {
            IncomeAccountDetail income_for_instructor_detail = new IncomeAccountDetail();
            income_for_instructor_detail = income_for_instructor_detail.GetIncomeDetail(order_number, course_name);
            string instructor_name = income_for_instructor_detail.instructor_name;
            string datetime_transfer = income_for_instructor_detail.datetime_transfer;
            string email = "<EMAIL>";
            double income_for_instructor = income_for_instructor_detail.income_for_instructor;
            
            //-----------------------------------------------------------

            using (MailMessage mm = new MailMessage("<EMAIL>", email))
            {
                mm.Subject = "แจ้งเตือนการขายคอร์ส";
                mm.Body = "กราบสวัสดี คุณ " + instructor_name + "<br/>" +
                            "คุณขายคอร์ส : " + course_name + "ได้แล้ว เมื่อวันที่ / เวลา : " + datetime_transfer + "<br/>" +
                            "ได้รับจำนวนเงิน : " + income_for_instructor + "<br/>";
                mm.IsBodyHtml = true;
                SmtpClient smtp = new SmtpClient();
                smtp.Host = "smtp.gmail.com";
                smtp.EnableSsl = true;
                NetworkCredential NetworkCred = new NetworkCredential("<EMAIL>", "cbmhub2499");
                smtp.UseDefaultCredentials = true;
                smtp.Credentials = NetworkCred;
                smtp.Port = 587;
                smtp.Send(mm);
            }
        }

    }
}