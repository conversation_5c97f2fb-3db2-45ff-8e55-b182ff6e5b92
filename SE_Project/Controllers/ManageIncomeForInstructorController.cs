﻿using SE_Project.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SE_Project.Controllers
{
    public class ManageIncomeForInstructorController : Controller
    {
        public static string static_account_number;
        // GET: ManageIncomeForInstructor (จัดการรายได้ให้ผู้สอน)
        public ActionResult ManageIncomeForInstructor(string account_number, bool isLastAccount)
        {
            if (isLastAccount == true) //ถ้า isLastAccount เป็น True แสดงว่า admin เลือกเมนู "จัดการรายได้ให้ผู้สอน" จากหน้าหลักของ admin
            {
                IncomeAccount incomeAccount = new IncomeAccount();
                incomeAccount = incomeAccount.GetLastIncomeAccount();
                if(incomeAccount == null)
                {
                    account_number = "null";
                }
                else
                {
                    account_number = incomeAccount.account_number;
                }
                
            }
            
            static_account_number = account_number;
            ViewBag.account_number = static_account_number;
            IncomeAccountDetail incomeAccountDetails = new IncomeAccountDetail();
            List<IncomeAccountDetail> income_detail_lists = new List<IncomeAccountDetail>();
            income_detail_lists = incomeAccountDetails.GetIncomeDetailList(static_account_number);
            return View(income_detail_lists);
        }

    }
}