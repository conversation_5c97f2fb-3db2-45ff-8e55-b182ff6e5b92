﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;
using System.Net;
using System.Net.Mail;


namespace SE_Project.Controllers
{
    public class RegisNewStudentsController : Controller
    {
        // GET: RegisNewStudents
        public ActionResult RegisNewStudents()
        {
            return View(); //แสดงแบบฟอร์มการสมัครสมาชิก สำหรับผู้เรียน
        }

        [HttpPost]
        public string RegisNewStudents(UserInfo model)
        {            
            UserInfo userInfo = new UserInfo();

            var response = "";

            bool check = userInfo.CheckMailDuplicate(model.user_Email);
            if (check == false)
            {
                
                using (SE_Context_1 db = new SE_Context_1())
                {
                    model.user_Phone = "-";
                    model.user_Code = Guid.NewGuid().ToString();
                    model.userType_ID = 3; // ประเภทผู้ใช้ == ผู้เรียน
                    model.user_RegisTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

                    userInfo.SaveRegisNewUser(model);
                    //SaveRegisNewStudents(userInfo);
                    SendMailRegisNewStudents(model.user_Email);

                    response = "success";
                                      
                }
            }
            else
            {
                response = "error";
                
            }

            return response;// Json(response, JsonRequestBehavior.AllowGet);
        }

        //public bool CheckMailDuplicate(string user_email)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        var check = db.UserInfo.SingleOrDefault(u => u.user_Email == user_email);
        //        if (check != null)
        //        {
        //            return false; // Email ซ้ำ 
        //        }
        //        else
        //        {
        //            return true;  // Email ไม่ซ้ำ                   
        //        }
        //    }
        //}

        //public void SaveRegisNewStudents(UserInfo model)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        db.UserInfo.Add(model);
        //        db.SaveChanges();
        //    }
        //}

        public void SendMailRegisNewStudents(string email)
        {
            MailAddress to = new MailAddress(email);
            MailAddress from = new MailAddress("<EMAIL>");
            MailMessage message = new MailMessage(from, to);
            message.Subject = "ยืนยันการสมัครสมาชิก";
            message.Body = "ยินดีต้อนรับสู่เว็บไซต์ JBC-Learnfast ขอบคุณค่ะ ^^";

            SmtpClient client = new SmtpClient("smtp.gmail.com", 587)
            {
                // รหัสเก่า jbclearnfast999
                Credentials = new NetworkCredential("<EMAIL>", "cbmhub2499"),
                EnableSsl = true
            };
            // code in brackets above needed if authentication required 
            try
            {
                client.Send(message);
            }
            catch (SmtpException ex)
            {
                throw ex;
            }
        }
    }
}