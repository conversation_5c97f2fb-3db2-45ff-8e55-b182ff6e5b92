﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SE_Project.Models;

namespace SE_Project.Controllers
{
    public class MyLectureController : Controller
    {
        // GET: MyLecture
        public ActionResult MyLecture(string course_name, string course_id)
        {

            ViewBag.course_name = course_name;
            Lecture lecture = new Lecture();
            List<Lecture> lecture_dataList = lecture.GetLectureList(course_id);
            return View(lecture_dataList);
        }
    }
}