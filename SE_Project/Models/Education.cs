﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [Table("Education")]
    public partial class Education
    {
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        //public Education()
        //{
        //    this.InstructorsInfo = new HashSet<InstructorsInfo>();
        //}
        [Key]
        public string ID { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก วุฒิอการศึกษาขั้นต่ำ (ปริญญา ตรี)")]
        public string edu_Bachelor { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก วุฒิอการ ระดับปริญญาตรีก่อน)")]
        //[Compare("edu_Bachelor", ErrorMessage = "กรุณากรอก ก่อนหน้า")]
        public string edu_Master { get; set; }
        public string edu_Doctor { get; set; }
        public string edu_Other { get; set; }

        public void SaveUserEducation(Education education)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.Education.Add(education);
                db.SaveChanges();
            }
        }
    }
   
}