﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace SE_Project.Models
{
    [Table("CourseStatus")]
    public partial class CourseStatus
    {
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        //public CourseStatus()
        //{
        //    this.Course = new HashSet<Course>();
        //}
        [Key]
        public int ID { get; set; }
        public string courseStatus_Name { get; set; }

        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
       // public virtual ICollection<Course> Course { get; set; }
    }
}