﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SE_Project.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class SE_COS4101Entities2 : DbContext
    {
        public SE_COS4101Entities2()
            : base("name=SE_COS4101Entities2")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        //public virtual DbSet<ActivityInfo> ActivityInfo { get; set; }
        ////public virtual DbSet<StudentsInfo> StudentsInfo { get; set; }
        //public virtual DbSet<UserInfo> UserInfo { get; set; }
        //public virtual DbSet<UserType> UserType { get; set; }
        //public virtual DbSet<Education> Education { get; set; }
        ////public virtual DbSet<InstructorsInfo> InstructorsInfo { get; set; }
        //public virtual DbSet<Category> Category { get; set; }
        //public virtual DbSet<CourseStatus> CourseStatus { get; set; }
        //public virtual DbSet<LectureStatus> LectureStatus { get; set; }
        //public virtual DbSet<Lecture> Lecture { get; set; }
        //public virtual DbSet<Course> Course { get; set; }
    }
}
