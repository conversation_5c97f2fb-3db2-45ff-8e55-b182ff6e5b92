﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SE_Project.Models
{
    [Table("ForgetPasswordInfo")]
    public partial class ForgetPasswordInfo
    {
        [Key]
        public int Id { set; get; }
        public string Email { set; get; }
        public string Token { set; get; }
        public string CreateTimeToken { set; get; }
        public bool IsCheck { set; get; }

        public void SaveUrlToken(string userEmail, string token, string createTime,bool isCheck)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                ForgetPasswordInfo passwordInfo = new ForgetPasswordInfo();
                passwordInfo.Email = userEmail;
                passwordInfo.Token = token;
                passwordInfo.CreateTimeToken = DateTime.Now.ToShortTimeString();
                passwordInfo.IsCheck = true;

                db.ForgetPasswordInfo.Add(passwordInfo);
                db.SaveChanges();
                //
            }
        }

        //public string GetCreateTimeToken(string token)
        //{
        //    SE_Context_1 db = new SE_Context_1();
        //    var create_time = "";
        //    string time_now = DateTime.Now.ToString("h:mm");

        //    var check = db.ForgetPasswordInfo.SingleOrDefault(u => u.Token == token);

        //    create_time = check.CreateTimeToken; // Get เวลาที่สร้าง Token

        //    return create_time;  // Return  ค่า เวลาที่บันทึก Token    เพื่อนำไปเปรียบเทียบกับเวลาปัจจุบัน
        //}

        public string GetCreateTimeToken(string user_email)
        {
            SE_Context_1 db = new SE_Context_1();
            var create_time = "";
            string time_now = DateTime.Now.ToString("h:mm");

            var check = db.ForgetPasswordInfo.Where(u => u.Email == user_email && u.IsCheck == true).FirstOrDefault();

            create_time = check.CreateTimeToken; // Get เวลาที่สร้าง Token

            return create_time;  // Return  ค่า เวลาที่บันทึก Token    เพื่อนำไปเปรียบเทียบกับเวลาปัจจุบัน

        }

        //public string GetCreateTimeTokens(string token)
        //{
        //    SE_Context_1 db = new SE_Context_1();
        //    var create_time = "";
        //    string time_now = DateTime.Now.ToString("h:mm");

        //    var check = db.ForgetPasswordInfo.SingleOrDefault(u => u.Token == token && u.IsCheck == true);

        //    create_time = check.CreateTimeToken; // Get เวลาที่สร้าง Token

        //    return create_time;  // Return  ค่า เวลาที่บันทึก Token    เพื่อนำไปเปรียบเทียบกับเวลาปัจจุบัน

        //}

        public bool CheckLastTokenExist(string user_email)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                ForgetPasswordInfo forget = new ForgetPasswordInfo();
                var check_user = db.ForgetPasswordInfo.Where(a => a.Email == user_email && a.IsCheck == true).FirstOrDefault();
                if (check_user != null)
                {
                    return true;
                }
                else
                {
                    return false; // กรณี กด forget ครั้งแรก  จะไม่พบ Email ใน ForgetPasswridInfo
                }

            }

        }
        public bool CheckTokenExpire(string token)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var check_token = db.ForgetPasswordInfo.Where(a => a.Token == token && a.IsCheck == true).SingleOrDefault();

                if (check_token != null)
                {
                    return true;
                }
                else
                {
                    return false;
                }
                //return check_token;
            }
        }

        public string GetLastToken(string user_email)
        {
            SE_Context_1 db = new SE_Context_1();
            var get_token = "";
            string time_now = DateTime.Now.ToString("h:mm");

            var check = db.ForgetPasswordInfo.SingleOrDefault(u => u.Email == user_email && u.IsCheck == true);

            get_token = check.Token; // Get เวลาที่สร้าง Token

            return get_token;
        }


        public void SetFlagTokenExpire(string user_email)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var setflag = db.ForgetPasswordInfo.Where(a => a.Email == user_email && a.IsCheck == true).FirstOrDefault();
                setflag.IsCheck = false;
                db.SaveChanges();
            }
        }

        public bool CheckLastToken(string token)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var last_token = db.ForgetPasswordInfo.Where(a => a.Token == token && a.IsCheck == true).SingleOrDefault();

                if (last_token != null)
                {
                    return true;  // เจอ Last token ที่ยังไม่ถูกใช้ โดย IsCheck มี สถานะเป็น True (Link Existing)
                }
                else
                {
                    return false; // ไม่เจอ Last token ก็หมายความว่า Link ใช้งานไม่ได้แล้ว
                }
            }

        }
    }
}