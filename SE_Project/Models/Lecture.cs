﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;

namespace SE_Project.Models
{
    [Table("Lecture")]
    public partial class Lecture
    {
        [Key]
        public string ID { get; set; }
        public string course_ID { get; set; }
        public string user_ID { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ชื่อเล็คเชอร์")]
        public string lecture_Name { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก รายละเอียดของเล็คเชอร์")]
        public string lecture_Description { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก เลือกไฟล์วิดีโอ")]
        [NotMapped]
        public HttpPostedFileBase[] videoFile_Name { get; set; }

        public int lectureStatus_ID { set; get; }
        public string folderPath { get; set; }

        //public DateTime create_DateLec { get; set; } 
        public string notAppr_Reason { set; get; }

        public string examiner_Name { set; get; }
        public string approve_Date { set; get; }
        public string lecture_CreateDate { set; get; }



        public void SaveLectureListInfo(Lecture lectureInfo)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.Lecture.Add(lectureInfo);
                db.SaveChanges();
            }
        }

        public List<Lecture> GetLectureList(string course_id)
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                List<Lecture> lectureList = db.Lecture.Where(x => x.course_ID == course_id).OrderBy(x=>x.lecture_CreateDate).ToList();

                return lectureList;
            }           
        }

        public void SetLectureListStatusCancel(string lecture_id ,string course_id, int lectureStatus_id)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update_lecStatus = db.Lecture.Find(lecture_id);
                update_lecStatus.lectureStatus_ID = lectureStatus_id;
                db.Entry(update_lecStatus).State = EntityState.Modified;
                db.SaveChanges();


                //var update_lecStatusList = (from l in db.Lecture where l.course_ID == course_id select l).ToList();

                //foreach (var update_lectItem in update_lecStatusList)
                //{
                //    update_lectItem.lectureStatus_ID = lectureStatus_id;
                //    db.SaveChanges();
                //}
            }
        }

        public void SetLectureStatusApprove(string lecture_id,string ins_name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update_lecStatus = db.Lecture.Find(lecture_id);
                update_lecStatus.lectureStatus_ID = 2; // อนุมัติ Lecture         
                update_lecStatus.examiner_Name = ins_name;
                update_lecStatus.approve_Date = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

                db.Entry(update_lecStatus).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
            }
        }
    }
}