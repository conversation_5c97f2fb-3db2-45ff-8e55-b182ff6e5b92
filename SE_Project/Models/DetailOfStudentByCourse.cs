﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [NotMapped]
    public class DetailOfStudentByCourse
    {
        
        public string student_name { get; set; }

        public string dateTime_OrderCourse { get; set; }

        public string student_Email { get; set; }

        public List<DetailOfStudentByCourse> GetDetailOfStudentByCoursesList(List<string> order_number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {

                
                  List<DetailOfStudentByCourse> student_detail_lists = (from OrderCourse in db.OrderCourse
                                          join UserInfo in db.UserInfo on OrderCourse.student_Name equals UserInfo.user_Name
                                          where order_number.Contains(OrderCourse.order_Number) && OrderCourse.orderStatus_ID == 3
                                          select new DetailOfStudentByCourse
                                          {
                                              student_name = OrderCourse.student_Name,
                                              dateTime_OrderCourse = OrderCourse.datetime_OrderCourse,
                                              student_Email = UserInfo.user_Email
                                          }).ToList();
                
                
                /*
                List<DetailOfStudentByCourse> student_detail_lists = (from OrderCourse in db.OrderCourse
                                                                      join UserInfo in db.UserInfo on OrderCourse.student_Name equals UserInfo.user_Name
                                                                      join IncomeAccountDetail in db.IncomeAccountDetail on OrderCourse.order_Number equals IncomeAccountDetail.order_number
                                                                      where order_number.Contains(OrderCourse.order_Number) && OrderCourse.orderStatus_ID == 3 && IncomeAccountDetail.transfer_status == true
                                                                      select new DetailOfStudentByCourse
                                                                      {
                                                                          student_name = OrderCourse.student_Name,
                                                                          dateTime_OrderCourse = OrderCourse.datetime_OrderCourse,
                                                                          student_Email = UserInfo.user_Email
                                                                      }).ToList();
                */
                return student_detail_lists;
            }


            
        }
    }
}