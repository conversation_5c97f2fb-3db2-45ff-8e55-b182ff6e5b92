﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.Ajax.Utilities;
using System.Web.Mvc;
using System.Security.Cryptography;

namespace SE_Project.Models
{
    [Table("OrderCourse")]
    public class OrderCourse
    {
        [Key]
        public string order_Number { get; set; }

        public string student_Name { get; set; }

        public int total_Course { get; set; }

        public int total_Price { get; set; }

        public int orderStatus_ID { get; set; }

        public string datetime_OrderCourse { get; set; }

        public string datetime_TransferNotify { get; set; }

        public string datetime_ApprovePayment { get; set; }

        public string examiner_Name { get; set; }

        public string image_path { get; set; }

        [NotMapped]
        public HttpPostedFileBase ImageFile { get; set; }


        //--------------------------------------------------------

        public dynamic GetLastOrderStatus(string user_Name)
        {
            SE_Context_1 db = new SE_Context_1();

            var query_last_order_status = (from ord in db.OrderCourse
                                           where ord.student_Name == user_Name
                                           orderby ord.order_Number descending
                                           select ord).FirstOrDefault();

            return query_last_order_status;
        }

        public OrderCourse GetLastOrderedCourse(string user_Name)
        {
            SE_Context_1 db = new SE_Context_1();

            var last_order = (from ord in db.OrderCourse
                              where ord.student_Name == user_Name
                              orderby ord.order_Number descending
                              select ord).ToList().FirstOrDefault();

            return last_order;
        }

        public void SaveOrderCourse(OrderCourse orderCourse)
        {
            //เก็บข้อมูลลง Database "OrderCourse"
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.OrderCourse.Add(orderCourse); //1.Add ข้อมูลลง Database "OrderCourse"
                db.SaveChanges();
            }
        }

        public void UpdateOrderCourse_PaymentNotify(OrderCourse payment_notify)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update = (from ord in db.OrderCourse
                              where ord.order_Number == payment_notify.order_Number
                              select ord).FirstOrDefault();

                update.orderStatus_ID = payment_notify.orderStatus_ID;
                update.datetime_TransferNotify = payment_notify.datetime_TransferNotify;
                update.image_path = payment_notify.image_path;

                db.SaveChanges();
            }
        }

        public void UpdateOrderCourse_ApprovePayment(OrderCourse approve_payment)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update = (from ord in db.OrderCourse
                              where ord.order_Number == approve_payment.order_Number
                              select ord).FirstOrDefault();

                update.orderStatus_ID = approve_payment.orderStatus_ID;
                update.datetime_ApprovePayment = approve_payment.datetime_ApprovePayment;
                update.examiner_Name = approve_payment.examiner_Name;

                db.SaveChanges();
            }
        }

        public List<OrderCourse> GetOrderCourseList()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<OrderCourse> order_course_lists =
                                  db.OrderCourse.OrderByDescending(ord => ord.order_Number).ToList();
                return order_course_lists;
            }
        }

        public OrderCourse GetOrderCourse(string order_Number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var order_course_lists = (from ord in db.OrderCourse
                                          where ord.order_Number == order_Number
                                          select ord).ToList().FirstOrDefault();
                return order_course_lists;
            }
        }

        public List<OrderCourse> GetOrderCourseData(List<string> order_number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<OrderCourse> order_course_lists = (from ord in db.OrderCourse
                                          where order_number.Contains(ord.order_Number)
                                          select ord).ToList();
                return order_course_lists;
            }
        }

        public List<string> GetOrderNumber_MyCourse(string student_name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<OrderCourse> order_data = (from cs in db.OrderCourse
                                                 where cs.student_Name == student_name && cs.orderStatus_ID == 3
                                                 select cs).ToList();

                List<string> order_number_list = new List<string>();

                foreach (var ord in order_data)
                {
                    order_number_list.Add(ord.order_Number);
                }

                return order_number_list;
            }
        }

        public List<string> GetCourseName_MyCourse(List<string> order_number_list)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<OrderCourseDetail> order_detail_data = (from cs in db.OrderCourseDetail
                                                where order_number_list.Contains(cs.order_Number)
                                                select cs).ToList();

                List<string> course_name_list = new List<string>();

                foreach (var ord in order_detail_data)
                {
                    course_name_list.Add(ord.course_Name);
                }

                return course_name_list;
            }
        }


    }
}