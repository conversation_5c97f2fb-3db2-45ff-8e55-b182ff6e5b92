using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Entity;

namespace SE_Project.Models
{
    public partial class SE_Context_1 : DbContext
    {
        public SE_Context_1() 
            : base("name=SE_ConnectionString")
        {

        }
        //public virtual DbSet<StudentsInfo> StudentsInfo { set; get; }
        public virtual DbSet<UserInfo> UserInfo { set; get; }
        public virtual DbSet<UserType> UserType { set; get; }  
        //public virtual DbSet<InstructorsInfo> InstructorsInfo { set; get; }
        public virtual DbSet<Education> Education { set; get; }
        public virtual DbSet<Category> Category { set; get; }
        public virtual DbSet<Course> Course { set; get; }
        public virtual DbSet<CourseStatus> CourseStatus { set; get; }
        public virtual DbSet<Lecture> Lecture { set; get; }
        public virtual DbSet<ForgetPasswordInfo> ForgetPasswordInfo { set; get; }

        public virtual DbSet<IncomeAccount> IncomeAccount { set; get; }

        public virtual DbSet<IncomeAccountDetail> IncomeAccountDetail { set; get; }

        public virtual DbSet<OrderCourse> OrderCourse { set; get; }

        public virtual DbSet<OrderCourseDetail> OrderCourseDetail { set; get; }

        public virtual DbSet<LectureStatus> LectureStatuses { set; get; }
        public virtual DbSet<Log> Log { set; get; }
        
        //public virtual DbSet<CourseViewModel> CourseViewModel { set; get; }
        
        //public virtual DbSet<Course> Course_1 { set; get; }
        //public virtual DbSet<Lecture> Lecture_1 { set; get; }


    }
}