<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="66c1651d4ed24ce89551d4a6ea64636f" Name="Diagram1" DisplayType="false" ZoomLevel="75">
        <EntityTypeShape EntityType="SE_COS4101Model.ActivityInfo" Width="1.875" PointX="10.875" PointY="1" />
        <EntityTypeShape EntityType="SE_COS4101Model.StudentsInfo" Width="1.5" PointX="1" PointY="1" />
        <EntityTypeShape EntityType="SE_COS4101Model.UserInfo" Width="1.5" PointX="3.75" PointY="3.75" />
        <EntityTypeShape EntityType="SE_COS4101Model.UserTypeInfo" Width="1.5" PointX="1.5" PointY="4" />
        <AssociationConnector Association="SE_COS4101Model.FK__UserInfo__userTy__531856C7" />
        <EntityTypeShape EntityType="SE_COS4101Model.Education" Width="1.5" PointX="5.875" PointY="1.125" />
        <EntityTypeShape EntityType="SE_COS4101Model.InstructorsInfo" Width="1.5" PointX="8.625" PointY="1.125" />
        <AssociationConnector Association="SE_COS4101Model.FK_InstructorsInfo_Education" />
        <AssociationConnector Association="SE_COS4101Model.FK_Education_Education" />
        <EntityTypeShape EntityType="SE_COS4101Model.Category" Width="1.5" PointX="11" PointY="2.75" />
        <EntityTypeShape EntityType="SE_COS4101Model.CourseStatus" Width="1.5" PointX="8.875" PointY="4.5" />
        <EntityTypeShape EntityType="SE_COS4101Model.LectureStatus" Width="1.5" PointX="11" PointY="5.5" />
        <EntityTypeShape EntityType="SE_COS4101Model.Lecture" Width="1.5" PointX="13.625" PointY="4.5" />
        <EntityTypeShape EntityType="SE_COS4101Model.Course" Width="1.5" PointX="13.5" PointY="1.125" />
        <AssociationConnector Association="SE_COS4101Model.FK_Course_Category" />
        <AssociationConnector Association="SE_COS4101Model.FK_Course_CourseStatus" ManuallyRouted="false" >
        </AssociationConnector>
        <AssociationConnector Association="SE_COS4101Model.FK_Course_Lecture" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>