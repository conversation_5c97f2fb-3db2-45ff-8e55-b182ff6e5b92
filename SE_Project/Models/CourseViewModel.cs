﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    public class CourseViewModel
    {     
        public string ID { set; get; }
        public string create_Date { get; set; }
        public string course_Name { get; set; }
        public int courseStatus_ID { get; set; }
        public string courseStatus_Name { get; set; }
        public int course_Price { get; set; }
        public int n1 { set; get; } // รวม Lecture
        public int n2 { set; get; } // รวม ยังไม่ตรวจ Lecture
        public int n3 { set; get; } // รวม อนุมัติ Lecture
        public int n4 { set; get; } // รวม สั้่งแก้ Lecture
        public int n5 { set; get; } // รวม รอตรวจ Lecturec
    }
}

//public Course course { set; get; }
//public Lecture lecture { set; get; }
//public CourseStatus courseStatuse { set; get; }
//public LectureStatus lectureStatus { set; get; }
//public Category category { set; get; }