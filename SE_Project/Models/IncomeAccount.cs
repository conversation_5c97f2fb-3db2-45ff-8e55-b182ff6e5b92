﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SE_Project.Models
{
    [Table("IncomeAccount")]
    public class IncomeAccount
    {
        [Key]
        public string account_number { get; set; }

        public bool account_status { get; set; }

        public string creator_name { get; set; } //ชื่อผู้สร้างบัญชี

        public string create_date { get; set; }

        public string cutoffer_name { get; set; } //ชื่อผู้ตัดบัญชี

        public string cutoff_date { get; set; }

        //----------------------------------------------------

        public void SaveIncomeAccount(IncomeAccount incomeAccount)
        {
            //เก็บข้อมูลลง Database "IncomeAccount"
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.IncomeAccount.Add(incomeAccount);
                db.SaveChanges();
            }
        }

        public List<IncomeAccount> GetIncomeAccountList()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<IncomeAccount> income_account_lists =
                                  db.IncomeAccount.OrderByDescending(ord => ord.account_number).ToList();
                return income_account_lists;
            }
        }

        public dynamic GetLastIncomeAccount()
        {
            SE_Context_1 db = new SE_Context_1();

            var query_last_income_account = (from ac in db.IncomeAccount
                                             orderby ac.account_number descending
                                             select ac).FirstOrDefault();

            return query_last_income_account;
        }

        public void UpdateIncomeAccount_Cutoff(IncomeAccount cutoff_account)
        {
            SE_Context_1 db = new SE_Context_1();

            var update = (from ac in db.IncomeAccount
                          where ac.account_number == cutoff_account.account_number
                          select ac).FirstOrDefault();

            update.cutoffer_name = cutoff_account.cutoffer_name;
            update.account_status = cutoff_account.account_status;
            update.cutoff_date = cutoff_account.cutoff_date;

            db.SaveChanges();
        }

    }
}