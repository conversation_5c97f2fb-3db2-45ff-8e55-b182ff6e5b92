﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [Table("Course")]
    public class Course
    {
        [Key]
        public string ID { get; set; }
        public string user_ID { get; set; }
        public string instructor_Name { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก เลือกหมวดหมู่")]
        public int category_ID { get; set; }
        public int courseStatus_ID { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ชื่อคอร์ส")]
        public string course_Name { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ราคาคอร์ส")]
        public int course_Price { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก รายละเอียดคอร์ส")]
        public string course_Description { get; set; }
        public string create_Date { get; set; }

        [NotMapped]
        public IEnumerable<Lecture> lecturelist { set; get; }

        [NotMapped]
        public bool isChecked { get; set; }

        //[NotMapped]
        //public virtual Lecture Lecture { get; set; }

        public bool CheckCourseNameDuplicate(string course_Name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var check = db.Course.SingleOrDefault(u => u.course_Name == course_Name);
                if (check != null)
                {
                    return true; // Email ซ้ำ 
                }
                else
                {
                    return false;  // Email ไม่ซ้ำ                   
                }
            }
        }

        public void SaveCourseInfo(Course courseInfo)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.Course.Add(courseInfo);
                db.SaveChanges();

            }
        }

        public List<Course> GetCourseList(string ins_name)
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                List<Course> courseList = db.Course.Where(x => x.instructor_Name == ins_name).ToList();
                return courseList;
            }
            
        }

        public List<Course> GetCourseDataList()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<Course> courseList = db.Course.OrderBy(x=>x.course_Price).ToList();
                return courseList;
            }
        }

        public void SetCourseStatusCancel(string course_id, int courseStatus_id)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update_courseStatus = db.Course.Find(course_id);
                update_courseStatus.courseStatus_ID = courseStatus_id;
                db.Entry(update_courseStatus).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
            }
        }

        public void SetCourseStatusApprove(string course_id,int courseStatus_id)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var update_courseStatus = db.Course.Find(course_id);
                update_courseStatus.courseStatus_ID = courseStatus_id;
                db.Entry(update_courseStatus).State = System.Data.Entity.EntityState.Modified;
                db.SaveChanges();
            }
        }

        public List<Course> GetCourseData(List<string> course_id)
        {
            SE_Context_1 db = new SE_Context_1();

            var query = (from c in db.Course
                         where course_id.Contains(c.ID)
                         select c).ToList();

            var courses_data = query.Select(c => new Course
            {
                course_Name = c.course_Name,
                course_Price = c.course_Price,
                instructor_Name = c.instructor_Name
            }).ToList();

            return courses_data;
        }

        public List<Course> GetCourseDetail(List<string> course_name_list)
        {
            SE_Context_1 db = new SE_Context_1();

            var query = (from c in db.Course
                         where course_name_list.Contains(c.course_Name)
                         select c).ToList();

            var courses_data = query.Select(c => new Course
            {
                ID = c.ID,
                course_Name = c.course_Name,
                instructor_Name = c.instructor_Name,
                course_Description = c.course_Description
            }).ToList();

            return courses_data;
        }

        



    }

    public class CourseListModel
    {
        public List<Course> Courses { get; set; }
    }
}