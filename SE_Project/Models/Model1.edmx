﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="SE_COS4101Model.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="ActivityInfo">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ActivityDescription" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="category_Name" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Course">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="category_ID" Type="int" />
          <Property Name="courseStatus_ID" Type="int" />
          <Property Name="course_Name" Type="nvarchar" MaxLength="100" />
          <Property Name="course_Price" Type="int" />
          <Property Name="course_Description" Type="nvarchar" MaxLength="300" />
          <Property Name="lecture_ID" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="CourseStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="courseStatus_Name" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="Education">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="edu_Bachelor" Type="nvarchar" MaxLength="300" />
          <Property Name="edu_Master" Type="nvarchar" MaxLength="300" />
          <Property Name="edu_Doctor" Type="nvarchar" MaxLength="300" />
          <Property Name="edu_Other" Type="nvarchar" MaxLength="300" />
        </EntityType>
        <EntityType Name="InstructorsInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ins_Code" Type="varchar" MaxLength="4" StoreGeneratedPattern="Computed" />
          <Property Name="ins_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="ins_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="ins_Phone" Type="nchar" MaxLength="20" />
          <Property Name="ins_Password" Type="nvarchar" MaxLength="30" />
          <Property Name="edu_ID" Type="int" />
          <Property Name="ins_SignTime" Type="datetime" />
        </EntityType>
        <EntityType Name="Lecture">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="lecture_Name" Type="nvarchar" MaxLength="100" />
          <Property Name="lecture_Description" Type="nvarchar" MaxLength="300" />
          <Property Name="lectureStatus_ID" Type="int" />
          <Property Name="videoFiles_Name" Type="nvarchar" MaxLength="100" />
          <Property Name="folderPath" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="LectureStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="lectureStatus_Name" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="StudentsInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="stu_Code" Type="varchar" MaxLength="4" StoreGeneratedPattern="Computed" />
          <Property Name="stu_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="stu_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="stu_Password" Type="nvarchar" MaxLength="30" />
          <Property Name="stu_SignTime" Type="datetime" />
        </EntityType>
        <EntityType Name="UserInfo">
          <Key>
            <PropertyRef Name="user_Code" />
          </Key>
          <Property Name="user_Code" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="user_Name" Type="nvarchar" MaxLength="50" />
          <Property Name="user_Email" Type="nvarchar" MaxLength="50" />
          <Property Name="user_Password" Type="nvarchar" MaxLength="50" />
          <Property Name="userType_ID" Type="int" />
        </EntityType>
        <EntityType Name="UserTypeInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserTypeDescription" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <Association Name="FK__UserInfo__userTy__531856C7">
          <End Role="UserTypeInfo" Type="Self.UserTypeInfo" Multiplicity="0..1" />
          <End Role="UserInfo" Type="Self.UserInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="UserTypeInfo">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="UserInfo">
              <PropertyRef Name="userType_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Course_Category">
          <End Role="Category" Type="Self.Category" Multiplicity="0..1" />
          <End Role="Course" Type="Self.Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Category">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="category_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Course_CourseStatus">
          <End Role="CourseStatus" Type="Self.CourseStatus" Multiplicity="0..1" />
          <End Role="Course" Type="Self.Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CourseStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="courseStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Course_Lecture">
          <End Role="Lecture" Type="Self.Lecture" Multiplicity="0..1" />
          <End Role="Course" Type="Self.Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Lecture">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="lecture_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Education_Education">
          <End Role="Education" Type="Self.Education" Multiplicity="1" />
          <End Role="Education1" Type="Self.Education" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="Education">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Education1">
              <PropertyRef Name="ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_InstructorsInfo_Education">
          <End Role="Education" Type="Self.Education" Multiplicity="0..1" />
          <End Role="InstructorsInfo" Type="Self.InstructorsInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Education">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="InstructorsInfo">
              <PropertyRef Name="edu_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="SE_COS4101ModelStoreContainer">
          <EntitySet Name="ActivityInfo" EntityType="Self.ActivityInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Category" EntityType="Self.Category" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Course" EntityType="Self.Course" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CourseStatus" EntityType="Self.CourseStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Education" EntityType="Self.Education" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="InstructorsInfo" EntityType="Self.InstructorsInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Lecture" EntityType="Self.Lecture" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="LectureStatus" EntityType="Self.LectureStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="StudentsInfo" EntityType="Self.StudentsInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserInfo" EntityType="Self.UserInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserTypeInfo" EntityType="Self.UserTypeInfo" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="FK__UserInfo__userTy__531856C7" Association="Self.FK__UserInfo__userTy__531856C7">
            <End Role="UserTypeInfo" EntitySet="UserTypeInfo" />
            <End Role="UserInfo" EntitySet="UserInfo" />
          </AssociationSet>
          <AssociationSet Name="FK_Course_Category" Association="Self.FK_Course_Category">
            <End Role="Category" EntitySet="Category" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
          <AssociationSet Name="FK_Course_CourseStatus" Association="Self.FK_Course_CourseStatus">
            <End Role="CourseStatus" EntitySet="CourseStatus" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
          <AssociationSet Name="FK_Course_Lecture" Association="Self.FK_Course_Lecture">
            <End Role="Lecture" EntitySet="Lecture" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
          <AssociationSet Name="FK_Education_Education" Association="Self.FK_Education_Education">
            <End Role="Education" EntitySet="Education" />
            <End Role="Education1" EntitySet="Education" />
          </AssociationSet>
          <AssociationSet Name="FK_InstructorsInfo_Education" Association="Self.FK_InstructorsInfo_Education">
            <End Role="Education" EntitySet="Education" />
            <End Role="InstructorsInfo" EntitySet="InstructorsInfo" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="SE_COS4101Model" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="SE_COS4101Entities2" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="ActivityInfo" EntityType="SE_COS4101Model.ActivityInfo" />
          <EntitySet Name="StudentsInfo" EntityType="SE_COS4101Model.StudentsInfo" />
          <EntitySet Name="UserInfo" EntityType="SE_COS4101Model.UserInfo" />
          <EntitySet Name="UserTypeInfo" EntityType="SE_COS4101Model.UserTypeInfo" />
          <AssociationSet Name="FK__UserInfo__userTy__531856C7" Association="SE_COS4101Model.FK__UserInfo__userTy__531856C7">
            <End Role="UserTypeInfo" EntitySet="UserTypeInfo" />
            <End Role="UserInfo" EntitySet="UserInfo" />
          </AssociationSet>
          <EntitySet Name="Education" EntityType="SE_COS4101Model.Education" />
          <EntitySet Name="InstructorsInfo" EntityType="SE_COS4101Model.InstructorsInfo" />
          <AssociationSet Name="FK_InstructorsInfo_Education" Association="SE_COS4101Model.FK_InstructorsInfo_Education">
            <End Role="Education" EntitySet="Education" />
            <End Role="InstructorsInfo" EntitySet="InstructorsInfo" />
          </AssociationSet>
          <AssociationSet Name="FK_Education_Education" Association="SE_COS4101Model.FK_Education_Education">
            <End Role="Education" EntitySet="Education" />
            <End Role="Education1" EntitySet="Education" />
          </AssociationSet>
          <EntitySet Name="Category" EntityType="SE_COS4101Model.Category" />
          <EntitySet Name="CourseStatus" EntityType="SE_COS4101Model.CourseStatus" />
          <EntitySet Name="LectureStatus" EntityType="SE_COS4101Model.LectureStatus" />
          <EntitySet Name="Lecture" EntityType="SE_COS4101Model.Lecture" />
          <EntitySet Name="Course" EntityType="SE_COS4101Model.Course" />
          <AssociationSet Name="FK_Course_Category" Association="SE_COS4101Model.FK_Course_Category">
            <End Role="Category" EntitySet="Category" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
          <AssociationSet Name="FK_Course_CourseStatus" Association="SE_COS4101Model.FK_Course_CourseStatus">
            <End Role="CourseStatus" EntitySet="CourseStatus" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
          <AssociationSet Name="FK_Course_Lecture" Association="SE_COS4101Model.FK_Course_Lecture">
            <End Role="Lecture" EntitySet="Lecture" />
            <End Role="Course" EntitySet="Course" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="ActivityInfo">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ActivityDescription" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="StudentsInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="stu_Code" Type="String" MaxLength="4" FixedLength="false" Unicode="false" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="stu_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="stu_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="stu_Password" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="stu_SignTime" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="UserInfo">
          <Key>
            <PropertyRef Name="user_Code" />
          </Key>
          <Property Name="user_Code" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="user_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="user_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="user_Password" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="userType_ID" Type="Int32" />
          <NavigationProperty Name="UserTypeInfo" Relationship="SE_COS4101Model.FK__UserInfo__userTy__531856C7" FromRole="UserInfo" ToRole="UserTypeInfo" />
        </EntityType>
        <EntityType Name="UserTypeInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserTypeDescription" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="UserInfo" Relationship="SE_COS4101Model.FK__UserInfo__userTy__531856C7" FromRole="UserTypeInfo" ToRole="UserInfo" />
        </EntityType>
        <Association Name="FK__UserInfo__userTy__531856C7">
          <End Type="SE_COS4101Model.UserTypeInfo" Role="UserTypeInfo" Multiplicity="0..1" />
          <End Type="SE_COS4101Model.UserInfo" Role="UserInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="UserTypeInfo">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="UserInfo">
              <PropertyRef Name="userType_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="Education">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="edu_Bachelor" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="edu_Master" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="edu_Doctor" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="edu_Other" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="InstructorsInfo" Relationship="SE_COS4101Model.FK_InstructorsInfo_Education" FromRole="Education" ToRole="InstructorsInfo" />
          <NavigationProperty Name="Education1" Relationship="SE_COS4101Model.FK_Education_Education" FromRole="Education" ToRole="Education1" />
          <NavigationProperty Name="Education2" Relationship="SE_COS4101Model.FK_Education_Education" FromRole="Education1" ToRole="Education" />
        </EntityType>
        <EntityType Name="InstructorsInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ins_Code" Type="String" MaxLength="4" FixedLength="false" Unicode="false" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="ins_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ins_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ins_Phone" Type="String" MaxLength="20" FixedLength="true" Unicode="true" />
          <Property Name="ins_Password" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="edu_ID" Type="Int32" />
          <Property Name="ins_SignTime" Type="DateTime" Precision="3" />
          <NavigationProperty Name="Education" Relationship="SE_COS4101Model.FK_InstructorsInfo_Education" FromRole="InstructorsInfo" ToRole="Education" />
        </EntityType>
        <Association Name="FK_InstructorsInfo_Education">
          <End Type="SE_COS4101Model.Education" Role="Education" Multiplicity="0..1" />
          <End Type="SE_COS4101Model.InstructorsInfo" Role="InstructorsInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Education">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="InstructorsInfo">
              <PropertyRef Name="edu_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Education_Education">
          <End Type="SE_COS4101Model.Education" Role="Education" Multiplicity="1" />
          <End Type="SE_COS4101Model.Education" Role="Education1" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="Education">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Education1">
              <PropertyRef Name="ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="category_Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Course" Relationship="SE_COS4101Model.FK_Course_Category" FromRole="Category" ToRole="Course" />
        </EntityType>
        <EntityType Name="CourseStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="courseStatus_Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Course" Relationship="SE_COS4101Model.FK_Course_CourseStatus" FromRole="CourseStatus" ToRole="Course" />
        </EntityType>
        <EntityType Name="LectureStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="lectureStatus_Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="Lecture">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="lecture_Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="lecture_Description" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="lectureStatus_ID" Type="Int32" />
          <Property Name="videoFiles_Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="folderPath" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Course" Relationship="SE_COS4101Model.FK_Course_Lecture" FromRole="Lecture" ToRole="Course" />
        </EntityType>
        <EntityType Name="Course">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="category_ID" Type="Int32" />
          <Property Name="courseStatus_ID" Type="Int32" />
          <Property Name="course_Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="course_Price" Type="Int32" />
          <Property Name="course_Description" Type="String" MaxLength="300" FixedLength="false" Unicode="true" />
          <Property Name="lecture_ID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Category" Relationship="SE_COS4101Model.FK_Course_Category" FromRole="Course" ToRole="Category" />
          <NavigationProperty Name="CourseStatus" Relationship="SE_COS4101Model.FK_Course_CourseStatus" FromRole="Course" ToRole="CourseStatus" />
          <NavigationProperty Name="Lecture" Relationship="SE_COS4101Model.FK_Course_Lecture" FromRole="Course" ToRole="Lecture" />
        </EntityType>
        <Association Name="FK_Course_Category">
          <End Type="SE_COS4101Model.Category" Role="Category" Multiplicity="0..1" />
          <End Type="SE_COS4101Model.Course" Role="Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Category">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="category_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Course_CourseStatus">
          <End Type="SE_COS4101Model.CourseStatus" Role="CourseStatus" Multiplicity="0..1" />
          <End Type="SE_COS4101Model.Course" Role="Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CourseStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="courseStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Course_Lecture">
          <End Type="SE_COS4101Model.Lecture" Role="Lecture" Multiplicity="0..1" />
          <End Type="SE_COS4101Model.Course" Role="Course" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Lecture">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Course">
              <PropertyRef Name="lecture_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="SE_COS4101ModelStoreContainer" CdmEntityContainer="SE_COS4101Entities2">
          <EntitySetMapping Name="ActivityInfo">
            <EntityTypeMapping TypeName="SE_COS4101Model.ActivityInfo">
              <MappingFragment StoreEntitySet="ActivityInfo">
                <ScalarProperty Name="ActivityDescription" ColumnName="ActivityDescription" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="StudentsInfo">
            <EntityTypeMapping TypeName="SE_COS4101Model.StudentsInfo">
              <MappingFragment StoreEntitySet="StudentsInfo">
                <ScalarProperty Name="stu_SignTime" ColumnName="stu_SignTime" />
                <ScalarProperty Name="stu_Password" ColumnName="stu_Password" />
                <ScalarProperty Name="stu_Email" ColumnName="stu_Email" />
                <ScalarProperty Name="stu_Name" ColumnName="stu_Name" />
                <ScalarProperty Name="stu_Code" ColumnName="stu_Code" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserInfo">
            <EntityTypeMapping TypeName="SE_COS4101Model.UserInfo">
              <MappingFragment StoreEntitySet="UserInfo">
                <ScalarProperty Name="userType_ID" ColumnName="userType_ID" />
                <ScalarProperty Name="user_Password" ColumnName="user_Password" />
                <ScalarProperty Name="user_Email" ColumnName="user_Email" />
                <ScalarProperty Name="user_Name" ColumnName="user_Name" />
                <ScalarProperty Name="user_Code" ColumnName="user_Code" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserTypeInfo">
            <EntityTypeMapping TypeName="SE_COS4101Model.UserTypeInfo">
              <MappingFragment StoreEntitySet="UserTypeInfo">
                <ScalarProperty Name="UserTypeDescription" ColumnName="UserTypeDescription" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Education">
            <EntityTypeMapping TypeName="SE_COS4101Model.Education">
              <MappingFragment StoreEntitySet="Education">
                <ScalarProperty Name="edu_Other" ColumnName="edu_Other" />
                <ScalarProperty Name="edu_Doctor" ColumnName="edu_Doctor" />
                <ScalarProperty Name="edu_Master" ColumnName="edu_Master" />
                <ScalarProperty Name="edu_Bachelor" ColumnName="edu_Bachelor" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="InstructorsInfo">
            <EntityTypeMapping TypeName="SE_COS4101Model.InstructorsInfo">
              <MappingFragment StoreEntitySet="InstructorsInfo">
                <ScalarProperty Name="ins_SignTime" ColumnName="ins_SignTime" />
                <ScalarProperty Name="edu_ID" ColumnName="edu_ID" />
                <ScalarProperty Name="ins_Password" ColumnName="ins_Password" />
                <ScalarProperty Name="ins_Phone" ColumnName="ins_Phone" />
                <ScalarProperty Name="ins_Email" ColumnName="ins_Email" />
                <ScalarProperty Name="ins_Name" ColumnName="ins_Name" />
                <ScalarProperty Name="ins_Code" ColumnName="ins_Code" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Category">
            <EntityTypeMapping TypeName="SE_COS4101Model.Category">
              <MappingFragment StoreEntitySet="Category">
                <ScalarProperty Name="category_Name" ColumnName="category_Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CourseStatus">
            <EntityTypeMapping TypeName="SE_COS4101Model.CourseStatus">
              <MappingFragment StoreEntitySet="CourseStatus">
                <ScalarProperty Name="courseStatus_Name" ColumnName="courseStatus_Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="LectureStatus">
            <EntityTypeMapping TypeName="SE_COS4101Model.LectureStatus">
              <MappingFragment StoreEntitySet="LectureStatus">
                <ScalarProperty Name="lectureStatus_Name" ColumnName="lectureStatus_Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Lecture">
            <EntityTypeMapping TypeName="SE_COS4101Model.Lecture">
              <MappingFragment StoreEntitySet="Lecture">
                <ScalarProperty Name="folderPath" ColumnName="folderPath" />
                <ScalarProperty Name="videoFiles_Name" ColumnName="videoFiles_Name" />
                <ScalarProperty Name="lectureStatus_ID" ColumnName="lectureStatus_ID" />
                <ScalarProperty Name="lecture_Description" ColumnName="lecture_Description" />
                <ScalarProperty Name="lecture_Name" ColumnName="lecture_Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Course">
            <EntityTypeMapping TypeName="SE_COS4101Model.Course">
              <MappingFragment StoreEntitySet="Course">
                <ScalarProperty Name="lecture_ID" ColumnName="lecture_ID" />
                <ScalarProperty Name="course_Description" ColumnName="course_Description" />
                <ScalarProperty Name="course_Price" ColumnName="course_Price" />
                <ScalarProperty Name="course_Name" ColumnName="course_Name" />
                <ScalarProperty Name="courseStatus_ID" ColumnName="courseStatus_ID" />
                <ScalarProperty Name="category_ID" ColumnName="category_ID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>