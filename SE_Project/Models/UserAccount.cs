﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using System.Web;

namespace SE_Project.Models
{
    [Table("UserAccount")]
    
    public class UserAccount
    {
        [Key]
        public int user_Id { get; set; }
        public Nullable<int> education_Id { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ชื่อ")]
        public string user_Name { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Email")]
        [DataType(DataType.EmailAddress)]
        [EmailAddress(ErrorMessage ="รูปแบบ Email ไม่ถูกต้อง")]
        public string user_Email { get; set; }

        public string user_Phone { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Password")]
        [DataType(DataType.Password)]
        [MinLength(6, ErrorMessage = "กรอก 6 ตัวขึ้นไป")]
        public string user_Password { get; set; }

        [DataType(DataType.Password)]
        [Compare("user_Password", ErrorMessage = "Password ไม่ตรงกัน")]
        public string user_ConfirmPassword { get; set; }

        public string user_Type { get; set; }
        public Nullable<System.DateTime> user_signTime { get; set; }

        public virtual Education Education { get; set; }
    }
}