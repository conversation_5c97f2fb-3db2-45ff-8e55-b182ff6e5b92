﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [Table("OrderCourseDetail")]
    public class OrderCourseDetail
    {
        [Key]
        public int ID { get; set; }

        public string instructor_Name { get; set; }

        public string course_Name { get; set; }

        public int course_Price { get; set; }

        //order_Number เป็น Foreign Key ที่มาจาก Primary Key ของ OrderCourseModel
        public string order_Number { get; set; }

        public void SaveOrderCourseDetail(List<OrderCourseDetail> orderCourseList)
        {

            using (SE_Context_1 db = new SE_Context_1())
            {
                //Add ข้อมูล List ลงฐานข้อมูล OrderCourseDetail 
                //โดยอ้างอิงจาก ForeignKey ฐานข้อมูล OrderCourseDetail
                //ที่ Reference กับ Primary key ของฐานข้อมูล OrderCourse
                foreach (var item in orderCourseList)
                {
                    db.OrderCourseDetail.AddRange(orderCourseList);
                }
                db.SaveChanges();
            }
        }

        public List<OrderCourseDetail> GetOrderCourseDetailList(string order_Number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<OrderCourseDetail> order_course_detail_lists = (from orc in db.OrderCourseDetail
                                                                     where orc.order_Number == order_Number
                                                                     select orc).ToList();
                return order_course_detail_lists;
            }
        }


    }
}