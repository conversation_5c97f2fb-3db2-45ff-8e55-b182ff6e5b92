﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SE_Project.Models
{
    [Table("Category")]
    public partial class Category
    {
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        //public Category()
        //{
        //    this.Course = new HashSet<Course>();
        //}
        [Key]
        public int ID { get; set; }
        public string category_Name { get; set; }
        //public bool IsChecked { set; get; }
        
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        //public virtual ICollection<Course> Course { get; set; }
    }
    public class CategoryModel
    {
        public List<Category> Categories { set; get; }
    }
}