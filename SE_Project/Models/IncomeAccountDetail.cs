﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SE_Project.Models
{
    [Table("IncomeAccountDetail")]
    public class IncomeAccountDetail
    {
        [Key]
        public int ID { get; set; }

        public string datetime_ApprovePayment { get; set; }

        public string order_number { get; set; }

        public string student_name { get; set; }

        public string instructor_name { get; set; }

        public string course_name { get; set; }

        public int course_price { get; set; }

        public double service_charge { get; set; }

        //income_for_instructor เป็น computed column ใน db ประเภทตัวแปรจึงต้องเป็น dynamic ไม่งั้นจะ error
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public double income_for_instructor { get; set; } 

        public bool transfer_status { get; set; }

        public string transferer { get; set; }

        public string datetime_transfer { get; set; }

        //reference_number เป็น Foreign Key ที่มาจาก Primary Key ของ IncomeAccount
        public string account_number { get; set; }

        public List<IncomeAccountDetail> GetLastIncomeDetailList(string account_number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<IncomeAccountDetail> income_detail_lists =
                                  (from icd in db.IncomeAccountDetail
                                   where icd.account_number == account_number
                                   select icd).ToList();
                return income_detail_lists;
            }
        }


        public List<IncomeAccountDetail> GetIncomeDetailList(string account_number)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<IncomeAccountDetail> income_detail_lists =
                                  (from icd in db.IncomeAccountDetail
                                   where icd.account_number == account_number
                                   select icd).ToList();
                return income_detail_lists;
            }
        }


        public void SaveIncomeAccountDetail(List<IncomeAccountDetail> income_detail_list)
        {
            //เก็บข้อมูลลง Database "IncomeAccountDetail"
            using (SE_Context_1 db = new SE_Context_1())
            {
                //Add ข้อมูล List ลงฐานข้อมูล IncomeAccountDetail 
                //โดยอ้างอิงจาก ForeignKey ฐานข้อมูล IncomeAccountDetail
                //ที่ Reference กับ Primary key ของฐานข้อมูล IncomeAccount
                foreach (var item in income_detail_list)
                {
                    db.IncomeAccountDetail.AddRange(income_detail_list);
                }
                db.SaveChanges();
            }
        }

        public List<string> GetOrderNumber(string course_Name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<IncomeAccountDetail> incomeAccountData = (from icd in db.IncomeAccountDetail
                                          where icd.course_name == course_Name
                                          select icd).ToList();

                List<string> order_number = new List<string>();

                foreach(var ord in incomeAccountData)
                {
                    order_number.Add(ord.order_number);
                }
                
               return order_number;
            }
        }

        public IncomeAccountDetail GetIncomeDetail(string order_Number, string course_Name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                IncomeAccountDetail income_detail = (from icd in db.IncomeAccountDetail
                                  where icd.order_number == order_Number && icd.course_name == course_Name
                                  select icd).FirstOrDefault();
                return income_detail;
            }
        }


        public void SaveTransferringIncomeToInstructor(IncomeAccountDetail income_for_instructor_detail)
        {
            SE_Context_1 db = new SE_Context_1();
            
            var update = (from icd in db.IncomeAccountDetail
                          where icd.order_number == income_for_instructor_detail.order_number && icd.course_name == income_for_instructor_detail.course_name
                          select icd).FirstOrDefault();
              
            update.transferer = income_for_instructor_detail.transferer;
            update.transfer_status = income_for_instructor_detail.transfer_status;
            update.datetime_transfer = income_for_instructor_detail.datetime_transfer;

            db.SaveChanges();
            
        }


    }
}