﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    //[Table("InstructorsInfo")]
    //public partial class InstructorsInfo 
    //{
        //public int ID { get; set; }
        //[Key]
        //public string ins_Code { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ชื่อ")]
        //public string ins_Name { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Email")]
        //public string ins_Email { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก หมายเลขโทรศัพท์")]
        //public string ins_Phone { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Password")]
        //[DataType(DataType.Password)]
        //[MinLength(6, ErrorMessage = "กรอก 6 ตัวขึ้นไป")]
        //public string ins_Password { get; set; }

        //[DataType(DataType.Password)]
        //[Compare("ins_Password", ErrorMessage = "Password ไม่ตรงกัน")]
        //public string ins_ConfirmPassword { get; set; }
        //public Nullable<System.DateTime> ins_SignTime { get; set; }
        //public string edu_ID { get; set; }

        //[NotMapped]
        //public virtual Education Education { get; set; }

    //}
    
}