﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    public class ResetNewPassword
    {
        
        [Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Password")]
        [DataType(DataType.Password)]
        [MinLength(6, ErrorMessage = "กรอก 6 ตัวขึ้นไป")]
        public string _password { set; get; }

        [DataType(DataType.Password)]
        [Compare("_password", ErrorMessage = "Password ไม่ตรงกัน")]
        public string _confirmPassword { set; get; }
    }
}