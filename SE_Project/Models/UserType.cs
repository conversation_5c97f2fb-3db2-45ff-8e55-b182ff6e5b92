﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [Table("UserType")]
    public partial class UserType
    {
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        //public UserType()
        //{
        //    this.UserInfo = new HashSet<UserInfo>();
        //}

        [Key]
        public int ID { get; set; }
        public string UserTypeDescription { get; set; }

        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        //public virtual ICollection<UserInfo> UserInfo { get; set; }
    }
}