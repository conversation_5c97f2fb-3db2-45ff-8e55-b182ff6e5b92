﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace SE_Project.Models
{
    [Table("Log")]
    public class Log
    {
        [Key]
        public string ID { set; get; }
        public string activityName { set; get; } // ชื่อกิจกรรม
        public string email { set; get; }    // ชื่อผู้ใช้
        public string userType { set; get; } //ประเภทผู้ใช้งาน
        public string courseName { set; get; }   // ชื่อคอร์ส
        public string courseStatus { set; get; } // สถานะคอร์ศ
        public string lecture_Name { set; get; } //  ชื่อเล็คเชอร์
        public string lectureStatus_Name { set; get; } //  สถานะเล็คเชอร์
        public string videoFile_Name { set; get; }     //  ชื่อไฟล์และวิดีโอไฟล์
        public string orderNumber { set; get; }//  หมายเลขใบสั่งซื้อ
        public string orderStatus { set; get; }//  สถานะใบสั่งซื้อ
        public string logTime { set; get; }  // วันและเวลาที่ทำกิจกรรม


        public void SaveLog(string _activityName, string _email, string _userType, string _logTime)
        {
            Log log = new Log();
            using (SE_Context_1 db = new SE_Context_1())
            {
                log.ID = Guid.NewGuid().ToString();
                log.activityName = _activityName;
                log.email = _email;
                log.userType = _userType;
                log.logTime = _logTime;
                db.Log.Add(log);
                db.SaveChanges();
            }
        }

        public void SaveLogChangePassword(string _activityName, string _email, string _logTime)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                Log log = new Log();  //Save Log ลง DataBase

                log.ID = Guid.NewGuid().ToString();
                log.activityName = _activityName;
                log.email = _email;
                log.logTime = _logTime;
                db.Log.Add(log);
                db.SaveChanges();
            }
        }

        public void SaveLogCreateCourse(string _activityName, string _email, string _courseName, string _courseStatus, string _logTime)
        {
            _logTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

            Log log = new Log();

            using (SE_Context_1 db = new SE_Context_1())
            {
                log.activityName = _activityName;
                log.email = _email;
                log.courseName = _courseName;
                log.courseStatus = _courseStatus;
                log.logTime = _logTime;

                //db.Log.Add(log);
                //db.SaveChanges();
            }

        }

        public void SaveLogCancelCourse()
        {
            using(SE_Context_1 db = new SE_Context_1())
            {
                Log log = new Log();

            }
        }

        public void SaveLogApproveLecture()
        {
            using (SE_Context_1 db = new SE_Context_1())
            {

            }
        }

        public void SaveLogOrderCourse(string _activityName, string _email, string _userType, string _orderNumber, string _orderStatus, string _logTime)
        {
            Log log = new Log();

            using (SE_Context_1 db = new SE_Context_1())
            {
                log.ID = Guid.NewGuid().ToString();
                log.activityName = _activityName;
                log.email = _email;
                log.userType = _userType;
                log.orderNumber = _orderNumber;
                log.orderStatus = _orderStatus;
                log.logTime = _logTime;

                db.Log.Add(log);
                db.SaveChanges();
            }
        }

        public void SaveLogPaymentNotify(string _activityName, string _email, string _userType, string _orderNumber, string _orderStatus, string _logTime)
        {
            Log log = new Log();

            using (SE_Context_1 db = new SE_Context_1())
            {
                log.ID = Guid.NewGuid().ToString();
                log.activityName = _activityName;
                log.email = _email;
                log.userType = _userType;
                log.orderNumber = _orderNumber;
                log.orderStatus = _orderStatus;
                log.logTime = _logTime;

                db.Log.Add(log);
                db.SaveChanges();
            }
        }

        public void SaveLogApprovePayment(string _activityName, string _email, string _userType, string _orderNumber, string _orderStatus, string _logTime)
        {
            Log log = new Log();

            using (SE_Context_1 db = new SE_Context_1())
            {
                log.ID = Guid.NewGuid().ToString();
                log.activityName = _activityName;
                log.email = _email;
                log.userType = _userType;
                log.orderNumber = _orderNumber;
                log.orderStatus = _orderStatus;
                log.logTime = _logTime;

                db.Log.Add(log);
                db.SaveChanges();
            }
        }
    }

    
}