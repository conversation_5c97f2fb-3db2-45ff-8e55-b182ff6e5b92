﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;

namespace SE_Project.Models
{
    //[Table("StudentsInfo")]
    //public string stu_ConfirmPassword { set; get; }
    //public partial class StudentsInfo
    //{
        ////[Key]
        //public string stu_Code { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก ชื่อ")]
        //public string stu_Name { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Email")]
        //[DataType(DataType.EmailAddress)]
        //[EmailAddress(ErrorMessage = "รูปแบบ Email ไม่ถูกต้อง")]
        //public string stu_Email { get; set; }

        //[Required(AllowEmptyStrings = false, ErrorMessage = "**กรุณากรอก Password")]
        //[DataType(DataType.Password)]
        //[MinLength(6, ErrorMessage = "กรอก 6 ตัวขึ้นไป")]
        //public string stu_Password { get; set; }

        //[DataType(DataType.Password)]
        //[Compare("stu_Password", ErrorMessage = "Password ไม่ตรงกัน")]
        //public string stu_ConfirmPassword { set; get; }
        //public Nullable<System.DateTime> stu_SignTime { get; set; }
    //}
}