﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Diagnostics;

namespace SE_Project.Models
{
    [Table("UserInfo")]
    public partial class UserInfo
    {
        [Key]
        public string user_Code { get; set; }

        public string user_Name { get; set; }

        public string user_Email { get; set; }

        public string user_Password { get; set; }

        public string user_Phone { set; get; }

        public int userType_ID { get; set; }

        public string education_ID { set; get; }

        public string user_RegisTime { set; get; }

        public string user_AccountNumber { set; get; }
        public string user_BankName { set; get; }
        //public string resetToken { set; get; }

        //public string createTime_Token { set; get; }

        [NotMapped]
        public virtual Education Education { get; set; }

//##########################################################################################

        public bool CheckMailDuplicate(string user_email)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var check = db.UserInfo.SingleOrDefault(u => u.user_Email == user_email);
                if (check != null)
                {
                    return true; // Email ซ้ำ 
                }
                else
                {
                    return false;  // Email ไม่ซ้ำ                   
                }
            }
        }

        public void SaveRegisNewUser(UserInfo model)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                db.UserInfo.Add(model);
                db.SaveChanges();
            }
        }

        public string GetUserType(string user_email, string user_password)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var user_type = "";
                var check = db.UserInfo.SingleOrDefault(u => u.user_Email == user_email && u.user_Password == user_password);
                if (check == null) // ไม่พบ user type
                {
                    user_type = "";
                    return user_type;
                }
                else
                {
                    user_type = check.userType_ID.ToString(); // Get User Type
                    return user_type;
                }
            }
        }

        public bool CheckMailOldUser(string email)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var check = db.UserInfo.SingleOrDefault(u => u.user_Email == email);
                if (check == null)
                {
                    return false; // ไม่พบ Email 
                }
                else
                {
                    return true;  // พบ Email                   
                }
            }
        }

        public UserInfo GetAccountDetail(string instructor_name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                UserInfo user_account_detail = (from ud in db.UserInfo
                                                     where ud.user_Name == instructor_name
                                                     select ud).FirstOrDefault();
                return user_account_detail;
            }
        }

        //public void ReplaceToken(string user_code, string reset_token, string createTime_token)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        var replace = db.UserInfo.Find(user_code);
        //        //var update_password = db.UserInfo.Include(p => p.user_Password==user_password).SingleOrDefault(p => p.user_Code == user_code);
        //        replace.resetToken = reset_token;
        //        replace.createTime_Token = createTime_token;

        //        db.Entry(replace).State = EntityState.Modified;
        //        db.SaveChanges();
        //    }
        //}

        //public string GetCreateTimeToken(string token)
        //{
        //    SE_Context_1 db = new SE_Context_1();
        //    var create_time = "";
        //    string time_now = DateTime.Now.ToString("h:mm");

        //    var check = db.UserInfo.SingleOrDefault(u => u.resetToken == token);

        //    create_time = check.createTime_Token; // Get เวลาที่สร้าง Token

        //    return create_time;  // Return  ค่า เวลาที่บันทึก Token    เพื่อนำไปเปรียบเทียบกับเวลาปัจจุบัน
        //}

        public void SaveChangePassword(string email,string user_password)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var get_user = db.UserInfo.Where(a => a.user_Email == email).FirstOrDefault();
                
                get_user.user_Password = user_password;
                db.SaveChanges();
            }
        }

        //public string GetUserName(string email)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        var user_name = "";
        //        var check = db.UserInfo.SingleOrDefault(u => u.user_Email == email);
        //        user_name = check.user_Name;
        //        return user_name;
        //    }

        //}
        public string GetUserID(string name)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                var user_id = "";
                var check = db.UserInfo.SingleOrDefault(u => u.user_Name == name);
                user_id = check.user_Code;
                return user_id;
            }

        }

        //public string GetUserCode(string email)
        //{
        //    using (SE_Context_1 db = new SE_Context_1())
        //    {
        //        var user_code = "";
        //        var check = db.UserInfo.SingleOrDefault(u => u.user_Email == email);
        //        user_code = check.user_Code;
        //        return user_code;
        //    }
        //}

        public List<string> GetUserEmail(List<OrderCourse> order_data_list)
        {
            using (SE_Context_1 db = new SE_Context_1())
            {
                List<UserInfo> user_data_list = (from u in db.UserInfo
                                                 where user_Email.Contains(u.user_Name)
                                                 select u).ToList();

                List<string> user_email = new List<string>();

                foreach (var u in user_data_list)
                {
                    user_email.Add(u.user_Name);
                }

                return user_email;
            }
        }
    }
}