﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SE_Project.Helper
{
    public static class Utility
    {
        public static string ConvetCourseStatus(int courseStatus_id)
        {
            switch (courseStatus_id)
            {
                case 1 : return "รอตรวจคอร์ส";
                case 2 : return "อนุมัติคอร์ส";
                case 3 : return "ยกเลิกคอร์สโดยผู้สอน";
                case 4 : return "ยกเลิกคอร์สโดยผู้ดูแลระบบ";
                default : return "";
            }            
        }

        public static string ConvertLectureStatus(int lectureStatus_id)
        {
            switch (lectureStatus_id)
            {
                case 1: return "รอตรวจเล็คเชอร์";
                case 2: return "อนุมัติเล็คเชอร์";
                case 3: return "ยกเลิกคอร์สโดยผู้สอน";
                case 4: return "ยกเลิกคอร์สโดยผู้ดูแลระบบ";              
                default: return "";
            }
        }

        public static string ConvertOrderStatus(int orderStatus_id)
        {
            switch (orderStatus_id)
            {
                case 1: return "รอชำระเงิน";
                case 2: return "รอตรวจสอบชำระเงิน";
                case 3: return "ชำระเงินแล้ว";
                case 4: return "ยกเลิก";
                default: return "";
            }
        }

        public static string ConvertIncomeAccountStatus(bool incomeAccount_status)
        {
            switch (incomeAccount_status)
            {
                case true: return "Active";
                case false: return "Inactive";
                default: return "";
            }
        }
    }
}