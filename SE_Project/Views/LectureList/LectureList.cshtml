﻿@model IEnumerable<SE_Project.Models.Lecture>
@{
    ViewBag.Title = "CourseListInstructorDetail";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;ชื่อคอร์ส : @ViewBag.courseName</p>
</div>
<div class="text-right">
    @if (ViewBag.courseStatus_id != 2)  // ปุ่มยกเลิกคอร์ส จะไม่แสดงก็ต่อเมื่อ คอร์สมีสถานะ อนุมัติคอร์ส
    {
        <button class="btn btn-danger btnConfirm_CancelCourse" type="button" id="@ViewBag.courseID"><i class="glyphicon glyphicon-remove" style="font-size:16px;"></i>&nbsp;<b>ยกเลิกคอร์ส</b></button>
    }

</div>

<div class="well" style="margin-top: 50px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 4%; text-align:center;">Lecture No.</th>
                <th style="width: 19%; ">ชื่อ Lecture</th>
                <th style="width: 6%; ">สถานะ Lecture</th>
                @*<th style="width: 1%; ">แก้ไข Lecture</th>*@
            </tr>

        </thead>
        <tbody>
            @{
                int num_lec = 0;
                foreach (var lecture_Item in Model)
                {
                    num_lec++;
                    //Session["numLec"] = num_lec;
                    <tr>
                        <td style="text-align:center;">@num_lec</td>
                        <td>@lecture_Item.lecture_Name</td>
                        <td>@SE_Project.Helper.Utility.ConvertLectureStatus(lecture_Item.lectureStatus_ID) </td>
                        
                    </tr>
                }
            }
        </tbody>

    </table>
</div>


<script>

    $(document).on('click', '.btnConfirm_CancelCourse', function () {
        var course_id = $(this).attr("id");
        //alert(course_id);
        //$("#btnDelete").attr("delete-id", equ_Id);
        swal({
            title: "",
            text: "คุณต้องการยกเลิกคอร์สเรียนใช่หรือไม่ ?",
            type: "warning",
            confirmButtonText: "ตกลง",
            confirmButtonColor: "#d95940",
            showConfirmButton: true,
            showCancelButton: true
        }, function () {
            $.ajax({
                type: "POST",
                url: "/LectureList/CancelCourseByInstructors",
                data: { "course_id": course_id },
                success: function () {
                    window.location.assign('/CourseListInstructors/CourseListInstructors');
                }
            });
        });

        
    });


</script>
