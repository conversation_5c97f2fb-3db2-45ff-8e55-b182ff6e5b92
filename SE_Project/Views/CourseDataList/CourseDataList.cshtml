﻿@model IEnumerable<SE_Project.Models.Course>
@{
    ViewBag.Title = "Course Data List";
}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;รายการข้อมูลคอร์สเรียน</p>
</div>

<div style="margin-top:60px;"></div>
@*<div class="col-sm-12">
        <div class="form-group col-sm-6">
            <div class="input-group add-on">
                <input class="form-control" placeholder="กรอกชื่อคอร์สเรียน" name="" id="" type="text">

                <div class="input-group-btn ">
                    <button class="btn btn-success text-right" type="submit"><i class="glyphicon glyphicon-search"></i>&nbsp;ค้นหา</button>
                </div>
            </div>
        </div>
    </div>*@

@*
    <div class="col-sm-12">
        <div class="breadcrumb" style="height: 45px;">
            <div class="col-sm-4">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;แสดงคอร์สเรียนทั้งหมด
                </label>
            </div>
            <div class="col-sm-4">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;คอร์สที่อนุมัติแล้ว
                </label>
            </div>

            <div class="col-sm-4">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;คอร์สที่กำลังตรวจสอบ
                </label>
            </div>
        </div>

    </div>
*@

<div class="col-sm-12">
    <div class="well">
        <table class="table table-striped table-bordered table-hover" id="example">
            <thead>
                <tr style="background-color:#64b5f6;">
                    <th style="width: 13%; ">วันที่สร้างคอร์ส</th>
                    <th style="width: 19%; ">ชื่อผู้สอน</th>
                    <th style="width: 24%; ">ชื่อคอร์ส</th>
                    <th style="width: 14%; ">สถานะคอร์ส</th>
                    <th style="width: 7%; ">ราคา</th>
                    <th style="width: 7%;">ตรวจสอบ</th>

                </tr>

            </thead>
            <tbody>
                @{
                    foreach (var courseDataItem in Model)
                    {
                        <tr>
                            <td>@courseDataItem.create_Date</td>
                            <td>@courseDataItem.instructor_Name</td>
                            <td>@courseDataItem.course_Name</td>
                            <td>@SE_Project.Helper.Utility.ConvetCourseStatus(courseDataItem.courseStatus_ID)</td>
                            <td>@courseDataItem.course_Price</td>


                            <td>
                                @if (courseDataItem.courseStatus_ID == 3 || courseDataItem.courseStatus_ID == 4) //ปุ่มตรวจสอบ จะ Disabled ก็ต่อเมื่อ คอร์สมีสถานะ ยกเลิกคอร์สโดยผู้สอน หรือ ผู้ดูแลระบบ
                                {
                                    <button class="btn btn-default" style="font-size:12px; background-color:gray;" disabled=""><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button>
                                }
                                else if (courseDataItem.courseStatus_ID == 2)
                                {
                                    <a href='@Url.Action("VerifyCourse", "VerifyCourse",
                                            new { course_id = courseDataItem.ID ,
                                                course_name = courseDataItem.course_Name ,
                                                instructor_name = courseDataItem.instructor_Name ,
                                                courseStatus_id = courseDataItem.courseStatus_ID  // Pass status_id == 3
                                            })' class="btn btn-primary" style="font-size:12px;">
                                        <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b>
                                    </a> // ปุ่ม สีน้ำเงิน
                                }
                                else
                                {
                                    <a href='@Url.Action("VerifyCourse", "VerifyCourse",
                                            new { course_id = courseDataItem.ID ,
                                                course_name = courseDataItem.course_Name ,
                                                instructor_name = courseDataItem.instructor_Name ,
                                                courseStatus_id = courseDataItem.courseStatus_ID  // Pass status_id == 2
                                            })' class="btn btn-warning" style="font-size:12px;">
                                        <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b>
                                    </a>// ปุ่ม สีส้ม
                                }

                            </td>

                        </tr>
                    }
                }


            </tbody>
        </table>
    </div>

</div>



