﻿
@{
    ViewBag.Title = "PaymentNotification";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;แจ้งชำระเงิน</p>
</div>

<div class="well" style="margin-top: 20px;">
    <div class="form-group col-sm-12">
        <div class="col-sm-3" style="text-align: right;">
            <span style="font-size:20px;">Order Number :</span>
        </div>
        <div class="col-sm-5">
            <select class="form-control">
                <option></option>
            </select>
        </div>
    </div>
    <div class="form-group col-sm-12">
        <div class="col-sm-3" style="text-align: right;">
            <span style="font-size:20px;">จำนวนเงินที่โอนเข้า : </span>
        </div>
        <div class="col-sm-3">
            <span style="font-size:20px;">xxxx บาท</span>
        </div>
    </div>

    <div class="form-group col-sm-12" style="margin-top:25px;">
        <div class="col-sm-3" style="text-align: right;">
            <span style="font-size:20px;">ธนาคารที่โอนเข้า : </span>
        </div>
        <div class="col-sm-6">
            <input type="radio" />
            <span style="font-size:20px;"><img src="~/image/kasikorn.png" width="35" />&nbsp; ธนาคารกสิกรไทย (499-2-54321-1)</span>
        </div>
    </div>

    <div class="form-group col-sm-12">
        <div class="col-sm-3"></div>
        <div class="col-sm-6">
            <input type="radio" />
            <span style="font-size:20px;"><img src="~/image/scb.png" width="35" />&nbsp; ธนาคารไทยพาณิชย์ (627-4-77832-3)</span>
        </div>
    </div>

    <div class="form-group col-sm-12" style="margin-top:20px;">
        <div class="col-sm-3" style="text-align: right;">
            <span style="font-size:20px;">สลิปหลักฐานการโอนเงิน : </span>
        </div>
        <div class="col-sm-3">
            <input type="file"style="font-size:18px;"/>
        </div>
    </div>
    <hr style="color:blueviolet;"/>
    <div style="text-align:center;">
        <button class="btn btn-success" id="" style="font-size: 18px; margin-top: 30px;">&nbsp;แจ้งโอน</button>
    </div>
</div>
