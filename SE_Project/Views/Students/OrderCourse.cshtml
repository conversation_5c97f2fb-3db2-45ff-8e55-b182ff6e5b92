﻿
@{
    ViewBag.Title = "OrderCourse";
}
<div class="container form-group well">
    @*<div class="col-md-12"><span style="font-size:22px;">ชื่อคอร์ส : </span></div>
        <div class="col-md-12"><span style="font-size:22px;">ราคา : </span></div>
        <div class="col-md-12"><span style="font-size:22px;">ชื่อผู้สอน :</span></div>*@



    <div class="from-group col-md-12">
        <div class="col-md-7">
            <span style="font-size:18px;">ชื่อคอร์ส : </span>
        </div>
        <div class="col-md-5"></div>
    </div>

    <div class="from-group col-md-12">
        <div class="col-md-6" style="margin-top: 10px;">
            <span style="font-size:18px;">ราคา : </span>
        </div>
        <div class="col-md-6"></div>
    </div>

    <div class="from-group col-md-12">
        <div class="col-md-6" style="margin-top: 10px;">
            <span style="font-size:18px;">ชื่อผู้สอน :</span>
        </div>
        <div class="col-md-6 text-right ">
            <button class="btn-success btn-lg" id="btnOrderCourse" style="font-size:24px;">สั่งซื้อคอร์สเรียน</button>
        </div>
    </div>

    <div class="from-group col-md-12">
        <div class="col-md-6" style="margin-top: 30px;">
            @*<span style="font-size:20px;">รายละเอียด :</span>*@
        </div>
        <div class="col-md-6 text-right">
            @*<button class="btn btn-success" style="font-size:22px;">สั่งซื้อคอร์สเรียน</button>*@
            
        </div>


    </div>

    <div class="from-group col-md-12">
        <div class="card">
            <div class="card-header">
                <span style="font-size:18px;">รายละเอียด :</span>
            </div>
            <div class="card-body">
                <p style="font-size: 16px;">
                    ขายภาพออนไลน์สร้างรายได้หลักแสนต่อเดือน จากประสบการณ์จริงรายได้จริง เนื้อหาตั้งแต่เริ่มต้นทั้งวิธีการและวิธีคิด ให้ภาพถ่ายทำเงินให้คุณได้อย่างต่อเนื่อง <br />
                    - เข้มข้นด้วย 3 PHASE บทเรียนหลัก <br />
                    - แถมบทพิเศษการแต่งภาพแบบนิตยสาร
                </p>
            </div>
        </div>
    </div>
        

        <div class="col-md-10">

        </div>
    </div>
    <label class="col-form-label" style="font-size: 18px; margin-top: 30px;">ให้ชำระเงิน โดยโอนเงินเช้าบัญชีธนาคาร ดังต่อไปนี้</label>
    <div class="" style="margin-top: 20px;">
        <table class="table table-striped table-bordered" id="">
            <thead style="font-size:18px;">
                <tr>
                    <th style="width: 5%; ">ธนาคาร</th>
                    <th style="width: 10%; ">สาขา</th>
                    <th style="width: 7%; ">เลขที่บัญชี</th>
                    <th style="width: 6%; ">ประเภทบัญชี</th>
                </tr>
            </thead>
            <tbody style="font-size:18px;">
                <tr>

                    <td><img src="~/image/kasikorn.png" width="45" />&nbsp;กสิกรไทย</td>
                    <td>ซีคอน ศรีนครินทร์</td>
                    <td>499-2-54321-1</td>
                    <td>ออมทรัพย์</td>
                </tr>
                <tr>
                    <td><img src="~/image/scb.png" width="45" />&nbsp;ไทยพาณิชย์</td>
                    <td>ซีคอน ศรีนครินทร์</td>
                    <td>627-4-77832-3</td>
                    <td>ออมทรัพย์</td>
                </tr>

            </tbody>
        </table>
    </div>

    <script src="~/Scripts/jquery-3.3.1.min.js"></script>
    <script>
        $(document).ready(function () {
            $("#btnOrderCourse").click(function () {
                swal({
                    title: "สั่งซื้อสำเร็จ",
                    text: "Order Number : @ViewBag.order_number",
                    type: "success",
                    confirmButtonText: "ตกลง",
                    confirmButtonColor: "#5cb85c",
                    showConfirmButton: true

                }, function () {
                    $.ajax({
                        
                    });
                });
            });
        });

    </script>

