﻿
@model IEnumerable<SE_Project.Models.OrderCourse>

@{
    ViewBag.Title = "OrderCourseList";

}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;รายการใบสั่งซื้อคอร์สเรียน</p>
</div>

<div style="margin-top:60px;"></div>
@*
    <!------------------------------------ส่วน Check box กรองข้อมูล------------------------------------>
    <div class="col-sm-12">
        <div class="breadcrumb" style="height: 45px;">

            <div class="col-sm-2">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;แสดงทั้งหมด
                </label>
            </div>

            <div class="col-sm-2">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;รอชำระเงิน
                </label>
            </div>

            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;รอตรวจสอบชำระเงิน
                </label>
            </div>

            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;อนุมัติชำระเงิน
                </label>
            </div>

            <div class="col-sm-2">
                <label class="checkbox-inline" style="font-size:16px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;ยกเลิก
                </label>
            </div>

        </div>
    </div>
    <!------------------------------------ส่วน Check box กรองข้อมูล------------------------------------>
*@
<!------------------------------------ส่วนตารางแสดงรายการใบสั่งซื้อ------------------------------------>
<div class="col-sm-12">
    <div class="well">
        <table class="table table-striped table-bordered table-hover" id="orderCourseList">
            <thead>
                <tr style="background-color:#64b5f6;">
                    <th style="width: 14%; text-align:center">วันที่/เวลาที่สั่งซื้อ</th>
                    <th style="width: 19%; text-align:center">หมายเลขใบสั่งซื้อ</th>
                    <th style="width: 24%; text-align:center">ชื่อลูกค้า</th>
                    <th style="width: 14%; text-align:center">สถานะใบสั่งซื้อ</th>
                    <th style="width: 7%; text-align:center">ตรวจสอบ</th>
                    <th style="width: 7%; text-align:center">ผู้ตรวจสอบ</th>
                    <th style="width: 16%; text-align:center">วันที่/เวลาที่ตรวจสอบ</th>
                </tr>
            </thead>

            <tbody>
                @{
                    foreach (var OrderCourseLists in Model)
                    {
                        <tr class="text-center">
                            <td>@OrderCourseLists.datetime_OrderCourse</td>
                            <td>@OrderCourseLists.order_Number</td>
                            <td>@OrderCourseLists.student_Name</td>
                            @*<td>@OrderCourseLists.order_Status</td>*@
                            <td>@SE_Project.Helper.Utility.ConvertOrderStatus(OrderCourseLists.orderStatus_ID)</td>

                            <td>
                                @if (OrderCourseLists.orderStatus_ID == 3)
                                {
                                    <a href='@Url.Action("ApprovePayment", "ApprovePayment",
                                                    new { order_Number = OrderCourseLists.order_Number})'
                                       class="btn btn-primary" style="font-size:12px;">
                                        <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b>
                                    </a>
                                }
                                else if (OrderCourseLists.orderStatus_ID == 1 || OrderCourseLists.orderStatus_ID == 2)
                                {
                                    <a href='@Url.Action("ApprovePayment", "ApprovePayment",
                                            new { order_Number = OrderCourseLists.order_Number})'
                                       class="btn btn-warning" style="font-size:12px;">
                                        <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b>
                                    </a>
                                }
                            </td>

                            @if (OrderCourseLists.examiner_Name == null)
                            {
                                <td class="text-center">-</td>
                            }
                            else
                            {
                                <td>@OrderCourseLists.examiner_Name</td>
                            }

                            @*----------------------------------------------*@

                            @if (OrderCourseLists.datetime_ApprovePayment == null)
                            {
                                <td class="text-center">-</td>
                            }
                            else
                            {
                                <td>@OrderCourseLists.datetime_ApprovePayment</td>
                            }
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>
<!------------------------------------ส่วนตารางแสดงรายการใบสั่งซื้อ------------------------------------>