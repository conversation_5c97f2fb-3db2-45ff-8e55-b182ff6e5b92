﻿@model IEnumerable<SE_Project.Models.IncomeAccountDetail>

@{
    ViewBag.Title = "ManageIncomeForInstructor";
    var account_number = ViewBag.account_number;
}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;รายการเงินส่วนแบ่งจากการขายคอร์ส</p>
</div>

<div style="margin-top:60px;"></div>

<div class="col-sm-12">
    <h3>หมายเลขบัญชี : @account_number</h3>
    <div class="well">
        <table class="table table-striped table-bordered table-hover" id="orderCourseList">
            <thead>
                <tr style="background-color:#64b5f6;">
                    <th>วันที่อนุมัติชำระเงิน</th>
                    <th>หมายเลขใบสั่งซื้อ</th>
                    <th>ชื่อผู้เรียน</th>
                    <th>ชื่อคอร์ส</th>
                    <th>ราคา</th>
                    <th>จำนวนเงินที่หักค่าบริการ</th>
                    <th>จำนวนเงินที่เหลือจากการหักค่าบริการ</th>
                    <th>สถานะการโอนเงิน</th>
                    <th>โอนเงินไปยังผู้สอน</th>
                    <th>ผู้ทำรายการ</th>
                    <th>วันที่/เวลาที่ทำรายการ</th>
                </tr>
            </thead>

            <tbody>
                @foreach (var IncomeAccountDetailList in Model)
                {
                    <tr>
                        <td>@IncomeAccountDetailList.datetime_ApprovePayment</td>
                        <td>@IncomeAccountDetailList.order_number</td>
                        <td>@IncomeAccountDetailList.student_name</td>
                        <td>@IncomeAccountDetailList.course_name</td>
                        <td>@IncomeAccountDetailList.course_price</td>
                        <td>@IncomeAccountDetailList.service_charge</td>
                        <td>@IncomeAccountDetailList.income_for_instructor</td>

                        @*สถานะการโอนเงิน และปุ่มโอนเงิน*@
                        @if (IncomeAccountDetailList.transfer_status == true)
                        {
                            <td>โอนเงินให้ผู้สอนแล้ว</td>

                            <td>
                                <a href='@Url.Action("TransferIncomeNotify", "TransferIncomeNotify",
                                                        new { order_Number = IncomeAccountDetailList.order_number, course_Name = IncomeAccountDetailList.course_name})'
                                   class="btn btn-primary" style="font-size:12px;">
                                    <i class="glyphicon glyphicon-usd"></i>&nbsp;<b>โอนเงินไปยังผู้สอน</b>
                                </a>
                            </td>
                        }
                        else
                        {
                            <td>ยังไม่ได้โอนเงิน</td>
                            <td>
                                <a href='@Url.Action("TransferIncomeNotify", "TransferIncomeNotify",
                                                        new { order_Number = IncomeAccountDetailList.order_number, course_Name = IncomeAccountDetailList.course_name})'
                                   class="btn btn-warning" style="font-size:12px;">
                                    <i class="glyphicon glyphicon-usd"></i>&nbsp;<b>โอนเงินไปยังผู้สอน</b>
                                </a>
                            </td>
                         }

                        @*ผู้ที่ทำรายการโอนเงิน และ วันที่ / เวลาที่โอนเงิน*@
                        @if(IncomeAccountDetailList.transferer == null)
                        {
                            <td>-</td>
                            <td>-</td>
                        }
                        else
                        {
                            <td>@IncomeAccountDetailList.transferer</td>
                            <td>@IncomeAccountDetailList.datetime_transfer</td>
                        }
                        
                    </tr>

                }


            </tbody>
        </table>
    </div>
</div>