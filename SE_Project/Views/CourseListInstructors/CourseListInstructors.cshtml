﻿@model IEnumerable<SE_Project.Models.Course>


@{
    ViewBag.Title = "Course List Instructors";
}



<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;คอร์สเรียนของผู้สอน</p>
</div>

<span style="font-size:20px;">ชื่อผู้สอน : @Session["user_Name"]</span>


<div class="well" style="margin-top: 20px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 13%;">วันที่สร้างคอร์ส</th>
                <th style="width: 24%;">ชื่อคอร์ส</th>
                <th style="width: 14%;">สถานะคอร์ส</th>
                <th style="width: 7%; ">ราคา</th>
                <th style="width: 7%; ">ตรวจสอบ</th>

            </tr>
        </thead>
        <tbody>
            @{
                foreach (var course_Item in Model)
                {
                    <tr>
                        <td>@course_Item.create_Date</td>
                        <td>@course_Item.course_Name</td>
                        
                        <td>@SE_Project.Helper.Utility.ConvetCourseStatus(course_Item.courseStatus_ID)</td>
                        <td>@course_Item.course_Price</td>

                        <td>
                            @*@SE_Project.Helper.Utility.ConvetStatus(@course_list.courseStatus_ID)*@
                            @if (course_Item.courseStatus_ID == 3 || course_Item.courseStatus_ID == 4) //ปุ่มตรวจสอบ จะ Disabled ก็ต่อเมื่อ คอร์สมีสถานะ ยกเลิกคอร์สโดยผู้สอน
                            {
                                <button class="btn btn-danger" disabled="" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button>
                            }
                            else if (course_Item.courseStatus_ID == 2) // ปุ่มตรวจสอบ จะเป็นสีน้ำเงินก็ต่่อเมื่อ คอร์สมีสถานะเป็น อนุมัติคอร์ส
                            {
                                <a href='@Url.Action("LectureList", "LectureList", new { course_id = course_Item.ID ,course_name = course_Item.course_Name , course_statusID = course_Item.courseStatus_ID})' class="btn btn-primary" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></a>
                            }
                            else  // ปุ่มตรวจสอบ จะเป็นสีน้ำส้ม ก็ต่่อเมื่อ คอร์สมีสถานะเป็น ยังไม่ตรวจคอร์ส
                            {
                                <a href='@Url.Action("LectureList", "LectureList", new { course_id = course_Item.ID ,course_name = course_Item.course_Name , course_statusID = course_Item.courseStatus_ID})' class="btn btn-warning" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></a>
                            }

                        </td>

                    </tr>
                }
            }

        </tbody>
    </table>
    
</div>

