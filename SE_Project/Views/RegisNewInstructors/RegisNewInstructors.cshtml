﻿@*@using (Html.BeginForm())
    {*@
@*@Html.AntiForgeryToken()*@
@*<div class="form-horizontal well col-md-10 col-md-offset-1">
            <h3 style="text-align: center; margin-top:0px;"><i class="glyphicon glyphicon-plus-sign"></i>&nbsp;สมัครใช้งานสำหรับผู้สอน</h3>
            <hr />

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <fieldset>


                <div class="form-group">
                    <div class="col-md-3 control-label">
                        <label>ชื่อ - สกุล<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">

                        @Html.EditorFor(model => model.user_Name, new { htmlAttributes = new { @class = "form-control", @placeholder = "ชื่อ - นามสกุล" } })
                        @Html.ValidationMessageFor(model => model.user_Name, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">
                        <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.user_Email, new { htmlAttributes = new { @class = "form-control", @placeholder = "Email" } })
                        @Html.ValidationMessageFor(model => model.user_Email, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">
                        <label class="col-form-label">หมายเลขโทรศัพท์<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.user_Phone, new { htmlAttributes = new { @class = "form-control", @placeholder = "หมายเลขโทรศัพท์" } })
                        @Html.ValidationMessageFor(model => model.user_Phone, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">

                    <div class="col-md-3 control-label">
                        <label class="col-form-label">วุฒิการศึกษา<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-1" style="text-align:right;">
                        @Html.CheckBox("nameCheckBox", false, new { style = "width:100px; height: 16px;" })
                        <input type="checkbox" id="chk1" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.ตรี<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">

                        @Html.EditorFor(model => model.Education.edu_Bachelor, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.ตรี", id = "txt_Bachelor", disabled = "disabled" } })
                        @Html.ValidationMessageFor(model => model.Education.edu_Bachelor, "", new { @class = "text-danger" })
                    </div>

                </div>
                <div class="form-group">
                    <div class="col-md-3 control-label">

                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk2" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.โท<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">
                        @Html.EditorFor(model => model.Education.edu_Master, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.โท", id = "txt_Master", disabled = "disabled" } })
                        @Html.ValidationMessageFor(model => model.Education.edu_Master, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">

                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk3" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.เอก<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>

                    <div class="col-md-7">
                        @Html.EditorFor(model => model.Education.edu_Doctor, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.เอก", id = "txt_Doctor", disabled = "disabled" } })
                        @Html.ValidationMessageFor(model => model.Education.edu_Doctor, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">

                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk4" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">อื่นๆ<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">
                        @Html.TextAreaFor(model => model.Education.edu_Other, new { @class = "form-control", @placeholder = "อื่นๆ", id = "txt_Other", disabled = "disabled" })

                    </div>
                </div>


                <div class="form-group" style="margin-top:40px;">
                    <div class="col-md-3 control-label">
                        <label class="col-form-label">Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.user_Password, new { htmlAttributes = new { @class = "form-control", @placeholder = "Password" } })
                        @Html.ValidationMessageFor(model => model.user_Password, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">
                        <label class="col-form-label">Confirm Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        @Html.EditorFor(model => model.user_ConfirmPassword, new { htmlAttributes = new { @class = "form-control", @placeholder = "Confirm Password" } })
                        @Html.ValidationMessageFor(model => model.user_ConfirmPassword, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div style="text-align:center;">

                        <button class="btn btn-success" type="submit"><i class="glyphicon glyphicon-save"></i>&nbsp;ยืนยันการสมัคร</button>
                    </div>
                </div>
            </fieldset>

        </div>
    }*@
<form id="ins_form" method="post" action="">
    <div class="form-horizontal well col-md-10 col-md-offset-1">
        <h3 style="text-align: center; margin-top:0px;"><i class="glyphicon glyphicon-plus-sign"></i>&nbsp;สมัครใช้งานสำหรับผู้สอน</h3>
        <hr />

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <fieldset>


            <div class="form-group">
                <div class="col-md-3 control-label">
                    <label>ชื่อ - สกุล<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                </div>
                <div class="col-md-9">
                    <input class="form-control" placeholder="ชื่อ - นามสกุล" id="insName" name="insName" type="text" required />
                    @*@Html.EditorFor(model => model.user_Name, new { htmlAttributes = new { @class = "form-control", @placeholder = "ชื่อ - นามสกุล" } })
                        @Html.ValidationMessageFor(model => model.user_Name, "", new { @class = "text-danger" })*@
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-3 control-label">
                    <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                </div>
                <div class="col-md-9">
                    <input class="form-control" placeholder="Email" id="insEmail" name="insEmail" type="text" required />
                    @*@Html.EditorFor(model => model.user_Email, new { htmlAttributes = new { @class = "form-control", @placeholder = "Email" } })
                        @Html.ValidationMessageFor(model => model.user_Email, "", new { @class = "text-danger" })*@
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-3 control-label">
                    <label class="col-form-label">หมายเลขโทรศัพท์<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                </div>
                <div class="col-md-9">
                    <input class="form-control" maxlength="10" placeholder="หมายเลขโทรศัพท์" id="insPhone" name="insPhone" type="text" required />
                    @*@Html.EditorFor(model => model.user_Phone, new { htmlAttributes = new { @class = "form-control", @placeholder = "หมายเลขโทรศัพท์" } })
                        @Html.ValidationMessageFor(model => model.user_Phone, "", new { @class = "text-danger" })*@
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-3 control-label">
                    <label class="col-form-label">เลขบัญชีธนาคาร<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                </div>
                <div class="col-md-9 ">
                    <input class="form-control" maxlength="10" placeholder="เลขบัญชีธนาคาร" id="insAccountNumber" name="insAccountNumber"/>
                </div>              
            </div>
            <div class="form-group">
                <div class="col-md-3 control-label">
                    <label class="col-form-label">เลือกธนาคาร<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                </div>
                <div class="col-md-9 ">
                    <select class="form-control" id="insBank" name="insBank">
                        <option value=""> -- กรุณาเลือกธนาคาร --</option>
                        <option>ธนาคารไทยพานิชย์</option>
                        <option>ธนาคารกรุงไทย</option>
                        <option>ธนาคารกสิกรไทย</option>
                        <option>ธนาคารออมสิน</option>
                    </select>
                </div>
            </div>
            <br />
                <div class="form-group">

                    <div class="col-md-3 control-label">
                        <label class="col-form-label">วุฒิการศึกษา<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk1" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.ตรี<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">
                        <input class="form-control" placeholder="ป.ตรี" id="insBachelor" name="insBachelor" type="text" disabled="disabled" />
                        @*@Html.EditorFor(model => model.Education.edu_Bachelor, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.ตรี", id = "txt_Bachelor", disabled = "disabled" } })
                @Html.ValidationMessageFor(model => model.Education.edu_Bachelor, "", new { @class = "text-danger" })*@
                    </div>

                </div>
                <div class="form-group">
                    <div class="col-md-3 control-label">


                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk2" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.โท<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">
                        <input class="form-control" placeholder="ป.โท" id="insMaster" name="insMaster" type="text" disabled="disabled" />
                        @*@Html.EditorFor(model => model.Education.edu_Master, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.โท", id = "txt_Master", disabled = "disabled" } })
                @Html.ValidationMessageFor(model => model.Education.edu_Master, "", new { @class = "text-danger" })*@
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">

                    </div>
                    <div class="col-md-1" style="text-align:right;">
                        @*@Html.CheckBox("nameCheckBox", false, new { style = "width:100px; height: 16px;" })*@
                        <input type="checkbox" id="chk3" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">ป.เอก<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>

                    <div class="col-md-7">
                        <input class="form-control" placeholder="ป.เอก" id="insDoctor" name="insDoctor" type="text" disabled="disabled" />
                        @*@Html.EditorFor(model => model.Education.edu_Doctor, new { htmlAttributes = new { @class = "form-control", @placeholder = "ป.เอก", id = "txt_Doctor", disabled = "disabled" } })
                @Html.ValidationMessageFor(model => model.Education.edu_Doctor, "", new { @class = "text-danger" })*@
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">


                    </div>
                    <div class="col-md-1" style="text-align:right;">

                        <input type="checkbox" id="chk4" style="width:18px; height:30px;" />
                    </div>
                    <div class="col-md-1">
                        <label class="col-form-label">อื่นๆ<i style="font-size: 23px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-7">
                        <textarea class="form-control" placeholder="อื่นๆ" id="insOther" name="insOther" disabled="disabled"></textarea>
                        @*<textarea class="form-control" placeholder="อื่นๆ" id="insOther" name="insOther" type="text" disabled="disabled" />*@
                        @*@Html.TextAreaFor(model => model.Education.edu_Other, new { @class = "form-control", @placeholder = "อื่นๆ", id = "txt_Other", disabled = "disabled" })*@

                    </div>
                </div>
                @*<input class="form-control text-box single-line password" data-val="true" data-val-minlength="กรอก 6 ตัวขึ้นไป" data-val-minlength-min="6" data-val-required="**กรุณากรอก Password" id="ins_Password" name="ins_Password" type="password" value="">*@

                <div class="form-group" style="margin-top:40px;">
                    <div class="col-md-3 control-label">
                        <label class="col-form-label">Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" placeholder="Password" id="insPassword" name="insPassword" type="password" required />
                        @*@Html.EditorFor(model => model.user_Password, new { htmlAttributes = new { @class = "form-control", @placeholder = "Password" } })
                @Html.ValidationMessageFor(model => model.user_Password, //insPassword insConfirmPassword "", new { @class = "text-danger" })*@
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-3 control-label">
                        <label class="col-form-label">Confirm Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" placeholder="Confirm Password" id="insConfirmPassword" name="insConfirmPassword" type="password" />
                        @*@Html.EditorFor(model => model.user_ConfirmPassword, new { htmlAttributes = new { @class = "form-control", @placeholder = "Confirm Password" } })
                @Html.ValidationMessageFor(model => model.user_ConfirmPassword, "", new { @class = "text-danger" })*@
                    </div>
                </div>

                <div class="form-group">
                    <div style="text-align:center;">

                        <button class="btn btn-success" type="button" id="btnSave"><i class="glyphicon glyphicon-save"></i>&nbsp;ยืนยันการสมัคร</button>
                    </div>
                </div>
</fieldset>

    </div>

</form>
<script>
    $(document).ready(function () {

        $("#btnSave").click(function () {

            var chk = CheckFormValidate();

            //alert(chk);
            if (chk == true) {

                var userInfo = {
                    user_Name: $("#insName").val(),
                    user_Email: $("#insEmail").val(),
                    user_Phone: $("#insPhone").val(),
                    user_Password: $("#insPassword").val(),
                    user_AccountNumber: $("#insAccountNumber").val(),
                    user_BankName: $("#insBank").val()
                };

                var education = { //insBachelor insMaster insDoctor insOther 
                    edu_Bachelor: $("#insBachelor").val(),
                    edu_Master: $("#insMaster").val(),
                    edu_Doctor: $("#insDoctor").val(),
                    edu_Other: $("#insOther").val()
                };

                var model = {
                    "user_model": userInfo,
                    "edu_model": education
                }

                $.ajax({
                    type: "post",
                    url: "RegisNewInstructors/RegisNewInstructors",
                    data: model,

                    success: function (response) {
                        if (response == "success") {
                            swal({
                                title: " ",
                                text: "สมัครสมาชิกสำเร็จ",
                                type: "success",
                                //timer: 2000,
                                showConfirmButton: true
                            }, function () {
                                window.location.assign('/Home/Index');
                            });
                        }
                        else {
                            swal({
                                title: "",
                                text: "มี Email นี้ในระบบแล้ว ",
                                type: "warning",
                                confirmButtonText: "ตกลง",
                                confirmButtonColor: "#d95940"
                            });
                        }
                    }
                });
            }

        });
    });

    function CheckFormValidate() {
        //insOther insBachelor
        if (!$("#ins_form").valid()) {

            return false;

        }
        if ($("#insBachelor").val() == '' && $("#insOther").val() == '' && $("#insMaster").val() == '' && $("#insDoctor").val() == '') {

            //alert("เลือก ป.ตรี หรือ วุฒิอื่นๆ");
            swal({
                title: "",
                text: "กรุณาเลือก วุฒิ ป.ตรี หรือ วุฒิอื่นๆ",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
            });
            return false;
        }

        if ($("#insBachelor").val() == '' && $("#insMaster").val() == '' && $("#insDoctor").val() != '' && $("#insOther").val() == ''
            || $("#insBachelor").val() == '' && $("#insMaster").val() == '' && $("#insDoctor").val() != '' && $("#insOther").val() != '')
        {
            //alert("กรุณาเลือกวุฒิ ป.ตรี และ ป.โท ก่อน");
            swal({
                title: "",
                text: "กรุณาเลือกวุฒิ ป.ตรี และ ป.โท ก่อน",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
            });
            return false;
        }

        if ($("#insBachelor").val() == '' && $("#insMaster").val() != '' && $("#insDoctor").val() != '' && $("#insOther").val() == ''
            || $("#insBachelor").val() == '' && $("#insMaster").val() != '' && $("#insDoctor").val() == '' && $("#insOther").val() != ''
            || $("#insBachelor").val() == '' && $("#insMaster").val() != '' && $("#insDoctor").val() != '' && $("#insOther").val() != ''
            || $("#insBachelor").val() == '' && $("#insMaster").val() != '' && $("#insDoctor").val() == '' && $("#insOther").val() == '') {
            //alert("กรุณาเลือกวุฒิ ป.ตรี ก่อน");
            swal({
                title: "",
                text: "กรุณาเลือกวุฒิ ป.ตรี ก่อน",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
            });
            return false;
        }
        if ($("#insBachelor").val() != '' && $("#insMaster").val() == '' && $("#insDoctor").val() != '' && $("#insOther").val() == ''
            || $("#insBachelor").val() != '' && $("#insMaster").val() == '' && $("#insDoctor").val() != '' && $("#insOther").val() != '')
        {
            swal({
                title: "",
                text: "กรุณาเลือกวุฒิ ป.โท ก่อน",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
            });
            return false;

        }


        return true;
    }



    $("#ins_form").validate({
        rules: {
            insName: {
                required: true
            },
            insEmail: {
                required: true,
                email: true
            },
            insPhone: {
                required: true,
                number: true
            },
            insPassword: {
                required: true,
                minlength: 8

            },
            insConfirmPassword: {
                equalTo: "#insPassword"
            },
            insAccountNumber: {
                required: true,
                number: true,
                minlength: 10
            },
            insBank: {
                required: true
            }

        },
        messages: {
            insName: {
                required: "**กรุณากรอก ชื่อ"
            },
            insEmail: {
                required: "**กรุณากรอก Email",
                email: "รูปแบบ Email ไม่ถูกต้อง"
            },
            insPhone: {
                required: "**กรุณากรอก หมายเลขโทรศัพท์",
                number: "ต้องเป็นตัวเลขเท่านั้น"
            },
            insPassword: {
                required: "**กรุณากรอก Password",
                minlength: "กรอก 8 ตัวขึ้นไป"
                //passwordMatch: "**Password ไม่ตรงกัน"
            },
            insConfirmPassword: {
                //required: '**กรุณา ยืนยัน Password',
                equalTo: '**Password ไม่ตรงกัน'
                //passwordMatch: "**Password ไม่ตรงกัน",
                //minlength: "**Password ไม่ตรงกัน"
            },
            insAccountNumber: {
                required: "**กรุณากรอก เลขบัญชีธนาคาร",
                number: "ต้องเป็นตัวเลขเท่านั้น",
                minlength: "กรอกให้ครบ 10 ตัวเลข"
            },
            insBank: {
                required: "**กรุณาเลือก ธนาคาร"
            }
        }
    });

        @ViewBag.message

        function ErrorMessage()
        {
            swal({
                title: "",
                text: "มี Email นี้ในระบบแล้ว ",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
                //showConfirmButton: false
            }//, function () {
                //window.location.assign('Index.aspx');
                //}
            );
        }
        function SuccessMessage() {
            swal({
                title: " ",
                text: "สมัครสมาชิกสำเร็จ",
                type: "success",
                timer: 2000,
                showConfirmButton: false
            }, function () {
                window.location.assign('/Home/Index');
            });
        }

    $(function () {
        $("#chk1").click(function () {
            if ($(this).is(":checked")) {
                $("#insBachelor").removeAttr("disabled");
                $("#insBachelor").focus();
                $("#insBachelor").val("");
            } else {
                $("#insBachelor").attr("disabled", "disabled");
                $("#insBachelor").val("");
            }
        });

        $("#chk2").click(function () {
            if ($(this).is(":checked")) {
                $("#insMaster").removeAttr("disabled");
                $("#insMaster").focus();
                $("#insMaster").val('');
            } else {
                $("#insMaster").attr("disabled", "disabled");
                $("#insMaster").val('');
            }
        });

        $("#chk3").click(function () {
            if ($(this).is(":checked")) {
                $("#insDoctor").removeAttr("disabled");
                $("#insDoctor").focus();
                $("#insDoctor").val('');
            } else {
                $("#insDoctor").attr("disabled", "disabled");
                $("#insDoctor").val('');
            }
        });

        $("#chk4").click(function () {
            if ($(this).is(":checked")) {
                $("#insOther").removeAttr("disabled");
                $("#insOther").focus();
                $("#insOther").val('');
            } else {
                $("#insOther").attr("disabled", "disabled");
                $("#insOther").val('');
            }
        });
    });




</script>

