﻿
@{
    ViewBag.Title = "ForgetPassword";
}


<div class="form-horizontal well col-md-6 col-md-offset-3">
    <h3 style="text-align: center; margin-top:0px;"><i class="glyphicon glyphicon-refresh"></i>&nbsp;ลืมรหัสผ่าน</h3>
    <hr />

    <div class="form-group">
        <div class="col-md-2">
            <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
        </div>
        <div class="col-md-10">
            <input class="form-control" placeholder="กรอก Email" id="email" />
            @*@Html.EditorFor(model => model.user_Email, new { htmlAttributes = new { @placeholder = "Email", @class = "form-control", id = "txt_StuName", } })
        @Html.ValidationMessageFor(model => model.user_Email, "", new { @class = "text-danger" })*@
           
        </div>

    </div>
    <div class="form-group" style="text-align:center">
        <button class="btn btn-success" style="font-size:18px;" id="btnSend" type="button"><i class="glyphicon glyphicon-send"></i>&nbsp;ส่งข้อมูล</button>
    </div>

</div>

@Scripts.Render("~/bundles/jquery")
<script>
    $(document).ready(function () {
        $("#btnSend").click(function () {
            var email = $('#email').val();
            //alert(email);
            if (email == '') {
                swal({
                    title: "",
                    text: "กรุณากรอก E-mail !!",
                    type: "warning",
                    confirmButtonText: "ตกลง",
                    confirmButtonColor: "#d95940"
                });
            } else {
                $.ajax({
                    type: "POST",
                    url: "/ForgetPassword/ForgetPassword",
                    data: { "user_email": email },
                    //  data: data,
                    success: function (response) {
                        if (response == "FoundEmail") {
                            swal({
                                title: "ยืนยัน Email สำเร็จ",
                                text: " ระบบจะส่ง Link URL ไปยัง Email ของท่าน เพื่อสร้างรหัสผ่านใหม่",
                                type: "success",
                                //timer: 2000,
                                confirmButtonText: "ตกลง",
                                confirmButtonColor: "#5cb85c",
                                showConfirmButton: true
                            }, function () {
                                window.location.assign('/Home/Index');
                            });
                        }
                        else { //response == "notFoundEmail"
                            swal({
                                title: "",
                                text: "ท่านยังไม่ได้เป็นสมาชิกในระบบ !!",
                                type: "warning",
                                confirmButtonText: "ตกลง",
                                confirmButtonColor: "#d95940"
                            });

                        }
                    }
                });
            }
            //alert(email);
           
        });
    });
    
</script>



