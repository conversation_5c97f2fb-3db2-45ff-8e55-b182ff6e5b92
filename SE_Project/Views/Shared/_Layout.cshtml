﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @*<link href="//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">*@

    <title>@ViewBag.Title - My ASP.NET Application</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/bundles/jqueryval")

    @*<script src="//kit.fontawesome.com/a076d05399.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">*@

    @*<script src="~/Scripts/notify.min.js" ></script>
        <script src="~/Scripts/notify.js" ></script>*@


    @*=================== Sweet Alert ======================*@
    <link href="~/Content/sweetalert.css" rel="stylesheet" />
    <script src="~/Scripts/sweetalert-dev.js"></script>
    <script src="~/Scripts/sweetalert.min.js"></script>

    @*==================== Data Tables ===================*@
    <link href="~/Content/dataTables.bootstrap.min.css" rel="stylesheet" />
    <script src="~/Scripts/jquery.dataTables.min.js"></script>
    <script src="~/Scripts/dataTables.bootstrap.min.js"></script>

    @*==================== DatePicker ===================*@
    <link href="~/Content/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>

</head>

<body>

    @*@model SE_Project.Models.UserLogin*@

    @*<div class="navbar navbar-inverse navbar-fixed-top">
                <div class="container">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        @Html.ActionLink("Application name", "Index", "Home", new { area = "" }, new { @class = "navbar-brand" })
                    </div>
                    <div class="navbar-collapse collapse">
                        <ul class="nav navbar-nav">
                            <li>@Html.ActionLink("Home", "Index", "Home")</li>
                            <li>@Html.ActionLink("About", "About", "Home")</li>
                            <li>@Html.ActionLink("Contact", "Contact", "Home")</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="container body-content">
            @RenderBody()
            <hr />
            <footer>
                <p>&copy; @DateTime.Now.Year - My ASP.NET Application</p>
            </footer>
        </div>
    *@

    @*box-shadow: 0px 9px 9px lightgrey;*@
    <nav class="navbar navbar-inverse navbar-global navbar-fixed-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>

                </button>

                @if (Session["UserType"] != "Instructors" && Session["UserType"] != "Admins")
                {
                    @*<a class="navbar-brand" href="~/Home/Index">JBC LearnFast.com </a>*@
                    <a href="~/Home/Index"><img src="~/image/LOGO_SE.PNG" style="width:120px; height:50px; " class="navbar-brand"></a>
                    @*<span style="color:cornflowerblue; font-size: 16px; margin-right: 20px;"><i class="glyphicon glyphicon-user"></i>&nbsp;:&nbsp; @Session["user_Name"] </span>*@
                }


            </div>
            <div id="navbar" class="collapse navbar-collapse">

                @*<span style="font-size:30px; font-family:sans-serif; color:darkgray;">@Session["UserType"]</span>*@


                @if (Session["UserType"] == "Instructors")
                {
                    <a class="navbar-brand" style="font-family:'Comic Sans MS'; font-size: 30px; " href="~/Home/InstructorMainPage">@Session["UserType"] </a>
                }
                else if (Session["UserType"] == "Admins")
                {
                    <a class="navbar-brand" style="font-family:'Comic Sans MS'; font-size: 30px; " href="~/Home/AdminMainPage">@Session["UserType"] </a>
                }

                <div class="nav navbar-nav navbar-user navbar-right" style="margin-top:7px;">

                    @if (Session["UserType"] == null)
                    {
                        <button type="button" id="btn_Login" class="btn btn-success"><i class="glyphicon glyphicon-log-in" style="font-size:16px;"></i>&nbsp;<span style="">Log in</span> </button>
                        //&nbsp;

                        <button id="btn_SignIn" class="btn btn-primary"><i class="glyphicon glyphicon-pencil" style="font-size:16px;"></i>&nbsp;<span style="">Sign in</span> </button>
                        //&nbsp;
                    }
                    else if (Session["UserType"] == "Students")
                    {
                        <a href="~/Home/StudentMainPage" style="color:white; font-size: 18px; margin-right: 20px;">คอร์สเรียนทั้งหมด</a>
                        <a href="~/MyCourse/MyCourse" style="color:white; font-size: 18px; margin-right: 20px;">คอร์สเรียนของฉัน</a>
                        <a href="~/PaymentNotify/PaymentNotify" style="color:white; font-size: 18px; margin-right: 20px;">แจ้งชำระเงิน</a>
                        <span style="color:cornflowerblue; font-size: 16px; margin-right: 20px;"><i class="glyphicon glyphicon-user"></i>&nbsp;:&nbsp; @Session["user_Name"] </span>
                        <a href="~/Login/Logout" class="btn btn-danger"><i class="glyphicon glyphicon-off" style="font-size:16px;"></i>&nbsp;<b style="font-size: 16px;">Logout</b></a>

                    }
                    else
                    {
                        <span style="color:cornflowerblue; font-size: 16px; margin-right: 20px;"><i class="glyphicon glyphicon-user"></i>&nbsp;:&nbsp; @Session["user_Name"] </span>
                        <a href="~/Login/Logout" class="btn btn-danger"><i class="glyphicon glyphicon-off" style="font-size:16px;"></i>&nbsp;<b style="font-size: 16px;">Logout</b></a>
                        @*<button id="" class="btn btn-danger"><i class="glyphicon glyphicon-off" style="font-size:16px;"></i>&nbsp;<b style="font-size: 16px;">Log out</b></button>*@
                        //&nbsp;
                    }

                    &nbsp; &nbsp;
                </div>
            </div><!--/.nav-collapse -->
        </div>
    </nav>
    @*<nav class="navbar-primary" >*@
    @*<a href="#" class="btn-expand-collapse"><span class="glyphicon glyphicon-menu-left"></span></a>*@
    @*<ul class="navbar-primary-menu">
        <li>*@
    @*<a href="#"><span class="glyphicon glyphicon-list-alt"></span><span class="nav-label">Dashboard</span></a>
        <a href="#"><span class="glyphicon glyphicon-envelope"></span><span class="nav-label">Profile</span></a>
        <a href="#"><span class="glyphicon glyphicon-cog"></span><span class="nav-label">Settings</span></a>
        <a href="#"><span class="glyphicon glyphicon-film"></span><span class="nav-label">Notification</span></a>
        <a href="#"><span class="glyphicon glyphicon-calendar"></span><span class="nav-label">Monitor</span></a>*@
    @*</li>
            </ul>
        </nav>*@

    <div class="loading">
        <img src="~/image/Loading.gif" style="width: 100px;" />
        <h4>กรุณารอสักครู่</h4>
    </div>

    @*<div class="content">*@
<div style="margin-top:40px;" class="container body-content">
    @RenderBody()

    @*===================== Sign in Modal ==================*@
    <div class="modal fade" id="SignIn_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">
        <div class="modal-dialog">

            <div class="modal-content ">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h3 class="modal-title" style="text-align:center;"><i class="glyphicon glyphicon-pencil" style="font-size:22px;"></i>&nbsp;Sign In</h3>
                </div>

                <div style="text-align:center; margin-top: 0px; height:60px;">
                    <div class="form-group">
                        <div class="col-sm-6">
                            <a href="~/RegisNewStudents/RegisNewStudents" type="button" class="btn btn-success btn-lg">@*<i class="fa fa-graduation-cap" style="font-size:25px;"></i>*@&nbsp;ลงทะเบียนสำหรับผู้เรียน</a>

                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-6">
                            <a href="~/RegisNewInstructors/RegisNewInstructors" type="button" class="btn btn-info btn-lg">@*<i class="fas fa-chalkboard-teacher" style="font-size:27px;"></i>*@&nbsp;ลงทะเบียนสำหรับผู้สอน</a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">

                </div>
            </div>
        </div>
    </div>
    @*=====================================================*@
    @*====================="Login", "Home" Log in Modal ==================*@

    @*<form id="loginForm">
        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })*@
    @*<form id="myForm">*@


    <div class="modal fade" id="Login_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h1 class="modal-title" style="text-align: center;"><i class="fa fa-user-circle" style="font-size:40px;"></i>&nbsp;Login</h1>
                </div>
                <div class="modal-body" style="">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <div class="col-md-2 control-label">
                                <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>

                            </div>
                            <div class="col-md-10">
                                <input class="form-control" placeholder="Email" id="userEmail" />
                                <span id="invalidEmail" hidden="hidden" style="color: red">**กรุณากรอก Email</span>
                                <span id="error_email"></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-2 control-label">
                                <label>Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                            </div>
                            <div class="col-md-10">
                                <input class="form-control" placeholder="Password" id="userPassword" type="password" />
                                <span id="invalidPassword" hidden="hidden" style="color: red">**กรุณากรอก Password</span>
                                <span id="error_password"></span>
                            </div>
                        </div>
                    </div>



                </div>

                <div class="modal-footer">
                    <div class="col-md-offset-2 col-md-10" style="text-align:right;">
                        @*<button type="submit" class="btn btn-warning" id="btn_ClickLogin"><i class="glyphicon glyphicon-log-in"></i>&nbsp;เข้าสู่ระบบ</button>*@
                        <button type="button" id="btn_ClickLogin" class="btn btn-success" style="font-size:16px;"><i class="glyphicon glyphicon-log-in"></i>&nbsp;เข้าสู่ระบบ</button>

                        <a href="~/ForgetPassword/ForgetPassword" class="btn btn-danger btn-sm" style="font-size:16px;"><i class="glyphicon glyphicon-refresh"></i>&nbsp;ลืมรหัสผ่าน</a>
                    </div>
                </div>

            </div>
        </div>
    </div>


    @*</form>*@

    @*}*@

    @*=====================================================*@
    @*</form>*@
</div>
    @*</div>*@
    <footer class="container">
        <p>&copy; @DateTime.Now.Year - JBC www.learnfast.com</p>
    </footer>
    <style>

        input,
        select,
        textarea {
            max-width: 100%;
        }

        .loading {
            top: 0;
            left: 0;
            right: 0;
            margin-top: 0px;
            text-align: center;
            margin-top: 140px;
            /*background-color:transparent;*/
        }

        html, body {
            height: 100%;
            margin: 0px;
        }

        .body {
            text-align: center;
            background-color: black;
            color: black;
        }

        .body-content {
            display: none;
        }

        .input-validation-error {
            border-color: Red;
            box-shadow: 0px 0px 3px #ff0000;
            /*color:red;*/
        }

        .error {
            color: red;
            border-color: red;
            box-shadow: 0px 0px 2px #ff0000;
            text-decoration-color: black;
        }
        /*.navbar-global {
            background-color: black;
        }

            .navbar-global .navbar-brand {
                color: white;
            }

            .navbar-global .navbar-user > li > a {
                color: white;
            }

        .navbar-primary {
            background-color: lightgray;
            bottom: 0px;
            left: 0px;
            position: absolute;
            top: 51px;
            width: 200px;
            z-index: 8;
            overflow: hidden;
            -webkit-transition: all 0.1s ease-in-out;
            -moz-transition: all 0.1s ease-in-out;
            transition: all 0.1s ease-in-out;
        }

            .navbar-primary.collapsed {
                width: 60px;
            }

                .navbar-primary.collapsed .glyphicon {
                    font-size: 22px;
                }

                .navbar-primary.collapsed .nav-label {
                    display: none;
                }

        .navbar-primary-menu,
        .navbar-primary-menu li {
            margin: 0;
            padding: 0;
            list-style: none;
        }

            .navbar-primary-menu li a {
                display: block;
                padding: 10px 18px;
                text-align: left;
                border-bottom: solid 1px #444;
                color: #ccc;
            }

                .navbar-primary-menu li a:hover {
                    background-color: #000;
                    text-decoration: none;
                    color: white;
                }

                .navbar-primary-menu li a .glyphicon {
                    margin-right: 6px;
                }

                .navbar-primary-menu li a:hover .glyphicon {
                    color: orchid;
                }

        .main-content {
            margin-top: 60px;
            margin-left: 200px;
            padding: 20px;
        }

        .collapsed + .main-content {
            margin-left: 60px;
        }*/
    </style>

    @*@Scripts.Render("~/bundles/jquery")*@

    @RenderSection("scripts", required: false)
    @Scripts.Render("~/bundles/jqueryval")
    @Scripts.Render("~/bundles/bootstrap")


    <script>


        $(document).ready(function () {
            $("#example").DataTable();
        });

        /*
        $(document).ready(function () {
            $('#orderCourseList').dataTable({
                "oLanguage": {
                        "sSearch": "ค้นหา:"
                },
                "order": {
                    [[3, "desc"]]
                },
            });
        });
        */

        $(document).ready(function () {
            $('#orderCourseList').dataTable({
                "order": [[1, "desc"]], //จัดเรียงหมายเลขใบสั่งซื้อ (Column ที่ 1) ให้เป็นใหม่สุดไปเก่าสุด
                "oLanguage": {
                    "sSearch": "ค้นหา:"
                },
            });
        });

        $('.loading').delay(900).fadeOut(300, function () {
            $('.body-content').fadeIn(300); //ค่อยๆสว่าง หน้าจอ
        });

        $(document).ready(function () {
            $("#btn_ClickLogin").click(function () {

                var email = $('#userEmail').val();
                var password = $('#userPassword').val();

                if (email == '' || password == '') {
                    var email = $('#userEmail').val();
                    var password = $('#userPassword').val();
                    if (email.length == '') {
                        $('#error_email').show();
                        $('#error_email').html('** กรุณากรอก Email');
                        $('#error_email').css('color', 'red');
                        $('#userEmail').css('border-color', 'red');

                        $('#userEmail').keyup(function () {
                            var email = $('#userEmail').val();
                            if (email.length == '') {
                                $('#error_email').show();
                                $('#error_email').focus();
                                $('#userEmail').css('border-color', 'red');
                                return false;
                            }
                            else {
                                $('#error_email').hide();
                                $('#userEmail').css('border-color', 'green');
                            }
                        });
                    }

                    if (password.length == '') {
                        $('#error_password').show();
                        $('#error_password').html('** กรุณากรอก Password');
                        $('#error_password').css('color', 'red');
                        $('#userPassword').css('border-color', 'red');

                        $('#userPassword').keyup(function () {
                            var email = $('#userPassword').val();
                            if (email.length == '') {
                                $('#error_password').show();
                                $('#error_password').focus();
                                $('#userPassword').css('border-color', 'red');
                                return false;
                            }
                            else {
                                $('#error_password').hide();
                                $('#userPassword').css('border-color', 'green');
                            }
                        });
                    }

                }
                else {
                    //var data = $('#loginForm').serialize();
                    var email = $('#userEmail').val();
                    var password = $('#userPassword').val();
                    //alert(email);
                    $.ajax({
                        type: "POST",
                        url: "/Login/Login",
                        data: { "user_email": email, "user_password": password },
                        //  data: data,
                        success: function (response) {
                            if (response == "Admins") {
                                window.location.assign('/Home/AdminMainPage');
                            }
                            else if (response == "Instructors") {
                                window.location.assign('/Home/InstructorMainPage');
                            }
                            else if (response == "Students") {
                                window.location.assign('/Home/StudentMainPage');
                            }
                            else {       //response == "NotFound"    ไม่พบ Email or Password
                                swal({
                                    title: "",
                                    text: "Email หรือ Password ไม่ถูกต้อง !!!",
                                    type: "warning",
                                    confirmButtonText: "ตกลง",
                                    confirmButtonColor: "#d95940"
                                });
                            }
                        }
                    });
                }
            });
        });

        function errorMsg() {
            swal({
                title: "",
                text: "Email หรือ Password ไม่ถูกต้อง !!!",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
            });
        }
        //function checkValidation() {
        //    var email = email = $('#userEmail').val();
        //    var pass = password = $('#userPassword').val();


        //    if (email == "") {
        //        $('#invalidEmail').show();
        //        $('#userEmail').css('border-color', 'red');
        //    } else {
        //        $('#invalidEmail').hide();
        //        $('#userEmail').css('border-color', '#ccc');
        //    }
        //    if (pass == "") {
        //        $('#invalidPassword').show();
        //        $('#userPassword').css('border-color', 'red');
        //    } else {
        //        $('#invalidPassword').hide();
        //        $('#userPassword').css('border-color', '#ccc');
        //    }
        //}


        $(document).ready(function () {
            $("#btn_Login").click(function () {
                $('#Login_Modal').modal();
            });
        });

        $(document).ready(function () {
            $("#btn_SignIn").click(function () {
                $('#SignIn_Modal').modal();
            });
        });
    </script>
</body>

</html>



