﻿@model IEnumerable<SE_Project.Models.Lecture>
@{
    ViewBag.Title = "Course";
}


<div class="alert-success">
    <div class="col-sm-6">
        <p style="font-size:22px;">&nbsp;ชื่อคอร์ส : @ViewBag.CourseName</p>
    </div>
    <div class="col-sm-6">
        <p style="font-size:22px;">&nbsp;ชื่อผู้สอน : @ViewBag.UserName</p>
    </div>


</div>
<div class="text-right">
    <a href='@Url.Action("CourseDataList","CourseDataList")' class="btn btn-outline-success text-left"><i class="glyphicon glyphicon-arrow-left"></i>&nbsp;<b>ย้อนกลับ</b></a>
    @if (ViewBag.courseStatus_id != 2)  // ปุ่มยกเลิกคอร์ส จะไม่แสดงก็ต่อเมื่อ คอร์สมีสถานะ อนุมัติคอร์ส
    {
        <button class="btn btn-danger btnConfirm_CancelCourse" type="button" id="@ViewBag.CourseId"><i class="glyphicon glyphicon-remove" style="font-size:16px;"></i>&nbsp;<b>ยกเลิกคอร์ส</b></button>
    }

</div>

<div class="well" style="margin-top: 50px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 4%; text-align:center;">Lecture No.</th>
                <th style="width: 19%; ">ชื่อ Lecture</th>
                <th style="width: 6%; ">สถานะ Lecture</th>
                <th style="width: 1%; ">ตรวจสอบ</th>
                <th style="width: 3%; ">ผู้ตรวจสอบ</th>
                <th style="width: 7%; ">วัน/เวลาที่ตรวจสอบ</th>
            </tr>

        </thead>
        <tbody>
            @{
                int num_lec = 0;
                foreach (var lec_Item in Model)
                {
                    num_lec++;
                    <tr>
                        <td style="text-align:center;">@num_lec</td>
                        <td>@lec_Item.lecture_Name</td>
                        <td>@SE_Project.Helper.Utility.ConvertLectureStatus(lec_Item.lectureStatus_ID)</td>
                        <td>
                            @if (lec_Item.lectureStatus_ID == 2) // สถานะ อนุมัติเล็คเชอร์
                            {
                                <button type="button" class="btn btn-primary btn-sm" onclick="VerifyLectureModal('@lec_Item.lecture_Name','@lec_Item.folderPath','@lec_Item.ID','@lec_Item.lectureStatus_ID')"><i class="glyphicon glyphicon-search"></i>&nbsp;ตรวจสอบ</button>
                            }
                            else
                            {
                                <button type="button" class="btn btn-warning btn-sm" onclick="VerifyLectureModal('@lec_Item.lecture_Name','@lec_Item.folderPath','@lec_Item.ID','@lec_Item.lectureStatus_ID')"><i class="glyphicon glyphicon-search"></i>&nbsp;ตรวจสอบ</button>
                            }

                        </td>
                        <td>@lec_Item.examiner_Name</td>
                        <td>@lec_Item.approve_Date</td>
                    </tr>
                }
            }

        </tbody>

    </table>
</div>

@*====================================================== Pop-Up หน้าจอ ตรวจสอบ Lecture =======================================================*@
<div class="modal fade" id="verifyLecture_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">

    <div class="modal-dialog" style="width:820px;">
        <div class="modal-content ">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>

                <div class="modal-title">
                    <div class="form-group col-sm-12" style="font-size: 20px; margin-bottom: 0px;">
                        @*<div class="col-sm-1"></div>
                        <div class="col-sm-6">*@
                            <h4>ชื่อคอร์ส : @ViewBag.CourseName</h4>
                        @*</div>
                        <div class="col-sm-5">*@
                            <h4 id="lec_name"></h4>
                        @*</div>*@
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-top: 20px; text-align:center;">
                    <div class="form-group col-sm-12">

                        @*<p id="v_path"></p>*@
                        <div id="v_path">

                        </div>

                        @*<video width="800" controls src='' autoplay>

                            </video>*@

                    </div>
                </div>
            </div>


            <div class="modal-footer">
                <div class="text-left">
                    <div class="col-sm-10">
                        <div style="padding-top:0px; margin-left: 130px;" id="appr_lec">

                            @*<button class="btn btn-danger btn-lg">ไม่อนุมัติ Lecture</button>*@
                        </div>

                        @*<div class="col-md-6" style="padding-top:0px;">*@
                        @*<span style="color: red; font-size: 23px;"><b>*</b></span>
                            <textarea class="form-control" placeholder="กรอกเหตุผลที่ไม่อนุมัติ Lecture" style="height:43px;"></textarea>*@
                        @*<textarea type="text" class="form-control" placeholder="กรอกเหตุผลที่ไม่อนุมัติ Lecture" />*@
                        @*</div>*@
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<script>

    function VerifyLectureModal(lecture_name, folder_path, lec_id, lec_status) {
        //alert(lec_status);
        $("#lec_name").html('ชื่อเล็คเชอร์ : ' + lecture_name + '');
        $("#v_path").html("<video width='500' controls src='" + folder_path + "' ></video>");

        if (lec_status == 2) {

            $("#appr_lec").html('<button class="btn-primary btn-block btn-lg btn_ApproveLecture" data-id=' + lec_id + '>อนุมัติ Lecture</button>').hide();
        }
        //if (lec_status == "ยังไม่ตรวจเล็คเชอร์") {
        else {
            $("#appr_lec").html('<button class="btn-primary btn-block btn-lg btn_ApproveLecture" data-id=' + lec_id + '>อนุมัติ Lecture</button>').show();
        }


        $("#verifyLecture_Modal").modal();
        //alert();
    }

    $('#verifyLecture_Modal').on('hidden.bs.modal', function () {
        $("#verifyLecture_Modal video").attr("src", $("#verifyLecture_Modal video").attr("src"));
        //$("#appr_lec").html('<button class="btn btn-success btn-block btn-lg btn_ApproveLecture" data-id=' + lec_id + '>อนุมัติ Lecture</button>').show();
        player.pause();
    });


    $(document).on('click', '.btn_ApproveLecture', function () {
        var lec_id = $(this).attr("data-id");

        var course_id = '@ViewBag.CourseId';
        //alert(course_name);
        //alert(lec_id);
        $.ajax({
            type: "post",
            url: "/VerifyCourse/ApproveLecture",    //RegisNewStudents/RegisNewStudents
            data: { "lecture_id": lec_id, "course_id": course_id },

            success: function (response) {

                if (response == "อนุมัติเล็คเชอร์สำเร็จ") {
                    swal({
                        title: response,
                        text: "",
                        type: "success",
                        showConfirmButton: true
                    }, function () {
                        location.reload();

                    });
                }
                else {
                    swal({
                        title: response,
                        text: "",
                        type: "success",
                        showConfirmButton: true
                    }, function () {
                        window.location.assign('@Url.Action("CourseDataList", "CourseDataList")');

                    });
                }

               
            }
        });
    });



    $(document).on('click', '.btnConfirm_CancelCourse', function () {
        var course_id = $(this).attr("id");
        //alert(course_id);
        swal({
            title: "",
            text: "คุณต้องการยกเลิกคอร์สเรียนใช่หรือไม่ ?",
            type: "warning",
            confirmButtonText: "ตกลง",
            confirmButtonColor: "#d95940",
            showConfirmButton: true,
            showCancelButton: true
        }, function () {
            $.ajax({
                type: "POST",
                url: "/VerifyCourse/CancelCourseByAdmin",
                data: { "course_id": course_id },
                success: function () {
                    window.location.assign('/CourseDataList/CourseDataList');
                }
            });
        });
    });
</script>
