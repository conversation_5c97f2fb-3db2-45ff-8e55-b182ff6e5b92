﻿@model IEnumerable<SE_Project.Models.IncomeAccountDetail>

@{
    ViewBag.Title = "TransferIncomeNotify";

    var account_number = ViewBag.account_number;
    var order_number = ViewBag.order_number;
    var course_name = ViewBag.course_name;
    var instructor_name = ViewBag.instructor_name;
    var accout_number_of_instructor = ViewBag.accout_number_of_instructor;
    var bank_name_of_instructor = ViewBag.bank_name_of_instructor;
    var income_for_instructor = ViewBag.income_for_instructor;
    var transfer_status = ViewBag.transfer_status;
}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;แจ้งโอนเงินไปยังผู้สอน</p>
</div>

<div style="margin-top:60px;"></div>

<div class="col-md-12">

    <div class="well " style="margin-top: 20px;">
        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:18px;">Order Number : </span>
            </div>
            <div class="col-sm-5">
                <span style="font-size:18px;" id="order_number">@order_number</span>
            </div>
        </div>
        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:18px;">ชื่อคอร์สเรียน : </span>
            </div>
            <div class="col-sm-5">
                <span style="font-size:18px;" id="course_name">@course_name</span>
            </div>
        </div>

        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:18px;">ชื่อผู้สอน : </span>
            </div>
            <div class="col-sm-5">
                <span style="font-size:18px;" id="">@instructor_name</span>

            </div>
        </div>

        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:18px;">หมายเลขบัญชี : </span>
            </div>
            <div class="col-sm-5">
                <span style="font-size:18px;" id="">@accout_number_of_instructor (@bank_name_of_instructor)</span>

            </div>
        </div>

        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:18px;">จำนวนเงินที่ต้องโอน : </span>
            </div>
            <div class="col-sm-5">
                <span style="font-size:18px;" id="">@income_for_instructor บาท</span>

            </div>
        </div>

        <hr style="color:blueviolet;" />
        <div style="text-align:center;">
            @if (transfer_status == true)
            {
                <button class="btn btn-default" type="submit" style="font-size: 18px; margin-top: 30px;" disabled>&nbsp;แจ้งโอนเงิน</button>
            }
            else
            {
                <button class="btn btn-success" id="transferBtn" value="@account_number" type="submit" onclick="TransferIncomeToInstructor()" style="font-size: 18px; margin-top: 30px;">&nbsp;แจ้งโอนเงิน</button>
              
            }
        </div>
    </div>

</div>

<script>

    function TransferIncomeToInstructor() {
      //  var account_number = document.getElementById('account_number)
        var order_number = $('#order_number').text();
        var course_name = $('#course_name').text();

        $.ajax({
            type: "POST",
            url: "/TransferIncomeNotify/TransferIncomeToInstructor",
            data: { 'order_number': order_number, 'course_name': course_name },
            success: function (response) {
                if (response == "True") {

                    swal({
                        title: "",
                        text: "แจ้งโอนเงินไปยังผู้สอนเรียบร้อยแล้ว",
                        type: "success",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#35D664"
                    }, function () {
                            var account_number = $('#transferBtn').val();
                            location.href = 'http://localhost:49957/ManageIncomeForInstructor/ManageIncomeForInstructor?account_number=' + account_number + '&isLastAccount=False'
                    });

                } else {
                    swal({
                        title: "",
                        text: "Something Wrong !",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            },
        });

    }



</script>