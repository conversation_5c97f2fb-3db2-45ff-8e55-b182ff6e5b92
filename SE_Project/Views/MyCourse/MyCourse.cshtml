﻿@model IEnumerable<SE_Project.Models.Course>
@{
    ViewBag.Title = "MyCourse";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class=""></i>&nbsp;คอร์สเรียนของฉัน</p>
</div>

<div class="col-sm-12">
    <div class="well">
        <table class="table table-striped table-bordered table-hover" id="example">
            <thead>
                <tr style="background-color:#64b5f6;">
                    <th style="width: 13%; ">ลำดับ</th>
                    <th style="width: 19%; ">ชื่อคอร์ส</th>
                    <th style="width: 24%; ">รายละเอียดคอร์ส</th>
                    <th style="width: 14%; ">ชื่อผู้สอน</th>
                    <th style="width: 7%; ">รายการเลคเชอร์</th>
                </tr>

            </thead>
            <tbody>
                @{
                    int i = 1;
                    foreach (var courseDataItem in Model)
                    {
                        <tr>
                            <td>@i</td>
                            <td>@courseDataItem.course_Name</td>
                            <td>@courseDataItem.course_Description</td>
                            <td>@courseDataItem.instructor_Name</td>
                            <td>
                                <a href='@Url.Action("MyLecture", "MyLecture",
                                        new {course_name = courseDataItem.course_Name, course_id = courseDataItem.ID })' class="btn btn-primary" style="font-size:12px;">
                                    <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ดูรายการเลคเชอร์</b>
                                </a>
                            </td>

                        </tr>
                        i++;
                    }
                }


            </tbody>
        </table>
    </div>

</div>
