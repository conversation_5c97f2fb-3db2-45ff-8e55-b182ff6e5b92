﻿@*@model SE_Project.Models.UserInfo*@

@*@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    <div class="form-horizontal well col-md-10 col-md-offset-1">
        <h3 style="text-align: center; margin-top:0px;"><i class="glyphicon glyphicon-plus-sign"></i>&nbsp;สมัครใช้งานสำหรับผู้เรียน</h3>
        <hr />

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label>ชื่อ - สกุล<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model=>model.user_Name, new { htmlAttributes = new { @placeholder = "ชื่อ - นามสกุล", @class = "form-control", id = "txt_StuName", autocomplete = "off" } })
                @Html.ValidationMessageFor(model => model.user_Name, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model => model.user_Email, new { htmlAttributes = new { @placeholder = "Email", @class = "form-control", id = "txt_StuEmail", autocomplete = "off" } })
                @Html.ValidationMessageFor(model => model.user_Email, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model => model.user_Password, new { htmlAttributes = new { @placeholder = "Password", @class = "form-control", id = "txt_StuPassword", autocomplete = "off" } })
                @Html.ValidationMessageFor(model => model.user_Password, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">Confirm Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">

                @Html.EditorFor(model => model.user_ConfirmPassword, new { htmlAttributes = new { @placeholder = "Confirm Password", @class = "form-control", autocomplete = "off" } })

                @Html.ValidationMessageFor(model => model.user_ConfirmPassword, "", new { @class = "text-danger" })

                @Html.HiddenFor(model => model.user_Phone, new { @Value = "-" } )
                
            </div>

        </div>

        <div class="form-group">
            <div style="text-align:center;">
                <button class="btn btn-success" id="" type="submit"><i class="glyphicon glyphicon-save"></i>&nbsp;ยืนยันการสมัคร</button>
            </div>
        </div>

    </div>

}*@

<form id="stu_form" method="post" action="">
    <div class="form-horizontal well col-md-10 col-md-offset-1">
        <h3 style="text-align: center; margin-top:0px;"><i class="glyphicon glyphicon-plus-sign"></i>&nbsp;สมัครใช้งานสำหรับผู้เรียน</h3>
        <hr />

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label>ชื่อ - สกุล<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                <input class="form-control required" placeholder="ชื่อ - นามสกุล" id="stuName" name="stuName" type="text" />
                @*@Html.EditorFor(model => model.user_Name, new { htmlAttributes = new { @placeholder = "ชื่อ - นามสกุล", @class = "form-control required", id = "txt_StuName", name= "stuName", type="text"} })*@
                @*@Html.ValidationMessageFor(model => model.user_Name, "", new { @class = "text-danger" })*@
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-3 control-label">

                <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                <input class="form-control" placeholder="Email" id="stuEmail" name="stuEmail" type="text" required />
                @*@Html.EditorFor(model => model.user_Email, new { htmlAttributes = new { @placeholder = "Email", @class = "form-control required", id = "txt_StuEmail", name = "stuEmail" } })*@
                @*@Html.ValidationMessageFor(model => model.user_Email, "", new { @class = "text-danger" })*@
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                <input class="form-control" type="password" placeholder="Password" id="stuPassword" name="stuPassword" required/>
                @*@Html.EditorFor(model => model.user_Password, new { htmlAttributes = new { @placeholder = "Password", @class = "form-control", id = "txt_StuPassword", autocomplete = "off" } })*@
                @*@Html.ValidationMessageFor(model => model.user_Password, "", new { @class = "text-danger" })*@
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">Confirm Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                <input class="form-control" type="password" id="stuConfirmPassword" placeholder="Confirm Password" name="stuConfirmPassword" />
                @*@Html.EditorFor(model => model.user_ConfirmPassword, new { htmlAttributes = new { @placeholder = "Confirm Password", @class = "form-control", autocomplete = "off" } })*@

                @*@Html.ValidationMessageFor(model => model.user_ConfirmPassword, "", new { @class = "text-danger" })*@

                @*@Html.HiddenFor(model => model.user_Phone, new { @Value = "-" })*@
                
            </div>

        </div>

        
        

        <div class="form-group">
            <div style="text-align:center;">
                <button class="btn btn-success" id="btnSave" type="button"><i class="glyphicon glyphicon-save"></i>&nbsp;ยืนยันการสมัคร</button>
            </div>
        </div>

    </div>
</form>
<script>
    $(document).ready(function () {
               
        $("#btnSave").click(function () {
                      
            var chk = CheckFormValidate();
            //alert(chk);
            if (chk == true) {

                var model = {                   
                    user_Name: $("#stuName").val(),
                    user_Email: $("#stuEmail").val(),
                    user_password: $("#stuPassword").val()                   
                };
                $.ajax({
                    type: "post",
                    url: "RegisNewStudents/RegisNewStudents",    //RegisNewStudents/RegisNewStudents
                    data: model,

                    success: function (response) {
                        if (response == "success") {
                            swal({
                                title: " ",
                                text: "สมัครสมาชิกสำเร็จ",
                                type: "success",
                                //timer: 2000,
                                showConfirmButton: true
                            }, function () {
                                window.location.assign('/Home/Index');
                            });
                        }
                        else {
                            swal({
                                title: "",
                                text: "มี Email นี้ในระบบแล้ว ",
                                type: "warning",
                                confirmButtonText: "ตกลง",
                                confirmButtonColor: "#d95940"                                
                            });
                        }                       
                    }
                });
            } 
        });
    });

    function CheckFormValidate() {

        if (!$("#stu_form").valid()) {
            return false;
        }
        return true;      
    }

    $("#stu_form").validate({
        rules: {
            stuName: {
                required: true
            },
            stuEmail: {
                required: true,
                email: true
            },
            stuPassword: {
                required: true,
                minlength: 8
                
            },
            stuConfirmPassword: {               
                equalTo: "#stuPassword"                              
            }
            
        },
        messages: {
            stuName: {
                required: "**กรุณากรอก ชื่อ"
            },
            stuEmail: {
                required: "**กรุณากรอก Email",
                email: "รูปแบบ Email ไม่ถูกต้อง"
            },
            stuPassword: {
                required: "**กรุณากรอก Password",
                minlength: "กรอก 8 ตัวขึ้นไป"
                //passwordMatch: "**Password ไม่ตรงกัน"
            },
            stuConfirmPassword: {
                //required: '**กรุณา ยืนยัน Password',
                equalTo: '**Password ไม่ตรงกัน'
                //passwordMatch: "**Password ไม่ตรงกัน",
                //minlength: "**Password ไม่ตรงกัน"               
            }
        }
    });

    @*@ViewBag.message*@

        //function ErrorMessage()
        //{
        //    swal({
        //        title: "",
        //        text: "มี Email นี้ในระบบแล้ว ",
        //        type: "warning",
        //        confirmButtonText: "ตกลง",
        //        confirmButtonColor: "#d95940"
        //        //showConfirmButton: false
        //    }//, function () {
        //        //window.location.assign('Index.aspx');
        //        //}
        //    );
        //}
        //function SuccessMessage() {
        //    swal({
        //        title: " ",
        //        text: "สมัครสมาชิกสำเร็จ",
        //        type: "success",
        //        timer: 2000,
        //        showConfirmButton: false
        //    }, function () {
        //        window.location.assign('/Home/Index');
        //    });
        //}

</script>


