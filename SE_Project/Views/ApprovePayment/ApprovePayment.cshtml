﻿
@{
    ViewBag.Title = "ApprovePayment";
    var viewBagOrderCourse = ViewBag.order_course;
    var viewBagOrderCourseDetailList = ViewBag.order_course_details;
    int rowNo = 0;
}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-tasks"></i>&nbsp;อนุมัติการชำระเงิน</p>
</div>

<!------------------------------------ส่วนแสดงรายละเอียดใบสั่งซื้อ------------------------------------>
<div class="col-sm-12">
    <p>รหัสใบสั่งซื้อ : @viewBagOrderCourse.order_Number</p>
    <p class="show_student_name" id="@viewBagOrderCourse.student_Name">ชื่อผู้เรียน : @viewBagOrderCourse.student_Name</p>
    <p>วันที่/เวลาที่สั่งซื้อ : @viewBagOrderCourse.datetime_OrderCourse</p>
</div>
<!------------------------------------ส่วนแสดงรายละเอียดใบสั่งซื้อ------------------------------------>
<!------------------------------------ส่วนตารางแสดงรายการคอร์สที่ซื้อ------------------------------------>
<div class="col-sm-12">
    <div class="well">
        <table class="table table-striped table-bordered table-hover">

            <thead>
                <tr style="background-color:#64b5f6;">
                    <th style="width: 14%; text-align:center">ลำดับ</th>
                    <th style="width: 19%; text-align:center">ชื่อคอร์สเรียน</th>
                    <th style="width: 24%; text-align:center">ชื่อผู้สอน</th>
                    <th style="width: 14%; text-align:center">ราคา</th>
                </tr>
            </thead>

            <tbody>

                @foreach (var courses_data in viewBagOrderCourseDetailList)
                {
                    <tr class="text-center">
                        <td>@(rowNo += 1)</td>
                        <td>@courses_data.course_Name</td>
                        <td>@courses_data.instructor_Name</td>
                        <td>@courses_data.course_Price</td>
                    </tr>
                }
            </tbody>

            <tfoot style="text-align: right;">
                <tr>
                    <td colspan="2" class="text-center" scope="row">จำนวนคอร์สที่สั่งซื้อ : @viewBagOrderCourse.total_Course</td>
                    <td colspan="3" class="text-center" scope="row">ราคารวมทั้งหมด : @viewBagOrderCourse.total_Price</td>
                </tr>
            </tfoot>

        </table>
    </div>
</div>
<!------------------------------------ส่วนตารางแสดงรายการคอร์สที่ซื้อ------------------------------------>
<!------------------------------------ส่วนแสดงรูปใบสลิป และปุ่ม อนุมัติชำระเงิน------------------------------------>
@if (viewBagOrderCourse.orderStatus_ID == 1)
{
    <hr style="color:blueviolet;" />
    <div style="text-align: center;">
        <input type="submit" value="อนุมัติชำระเงิน" class="btn btn-default" style="font-size: 18px" ; margin-top: 30px; disabled />
    </div>

}
else if (viewBagOrderCourse.orderStatus_ID == 2)
{

    <img src="@Url.Content(viewBagOrderCourse.image_path)" width="245" height="419" alt="สลิปโอนเงิน" class="center-block" />
    <hr style="color:blueviolet;" />
    <div style="text-align: center;">

        @*
        <a href='@Url.Action("ApprovePaymentOfOrder", "ApprovePayment",
                                                    new { order_Number = viewBagOrderCourse.order_Number})'
           class="btn btn-primary" style="font-size:18px;" margin-top: 30px;>
            <i class="fa fa-check"></i>&nbsp;<b>อนุมัติชำระเงิน</b>
        </a>
        *@

        <button class="btn btn-primary btnApprovePayment" id="@viewBagOrderCourse.order_Number" style="font-size:18px;" margin-top: 30px; onclick="CheckAccountIsActive()">
            <i class="fa fa-check"></i>&nbsp;<b>อนุมัติชำระเงิน</b>
        </button>

    </div>
}
else if (viewBagOrderCourse.orderStatus_ID == 3)
{
    <img src="@Url.Content(viewBagOrderCourse.image_path)" width="245" height="419" alt="สลิปโอนเงิน" class="center-block" />
    <hr style="color:blueviolet;" />
    <div style="text-align: center;">
        <input type="submit" value="อนุมัติชำระเงิน" class="btn btn-default" style="font-size: 18px" ; margin-top: 30px; disabled />
    </div>
}
<!------------------------------------ส่วนแสดงรูปใบสลิป และปุ่ม อนุมัติชำระเงิน------------------------------------>

<script type="text/javascript">

    var date_cutoff = ""
    var order_number = ""
    var account_number = ""
    var student_name = $(".show_student_name").attr("id");
   
    $(document).on('click', '.btnApprovePayment', function () {

        order_number = $(this).attr("id")

    });

    function CheckAccountIsActive() {
        $.ajax({
            type: "POST",
            url: "/ApprovePayment/CheckAccountIsActive",
            success: function (last_income_account) {
                account_number = last_income_account.account_number
                if (last_income_account.account_status == true) {
                    date_cutoff = last_income_account.cutoff_date
                    CheckExpiredAccount()
                } else {
                    swal({
                        title: "",
                        text: "กรุณาสร้างบัญชีรายได้ก่อนอนุมัติการชำระเงินของผู้เรียน",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            }
        });
    }

    //----------------------------------------------------------------

    function CheckExpiredAccount() {

        $.ajax({
            type: "POST",
            url: "/ApprovePayment/CheckExpiredAccount",
            data: { "date_cutoff" : date_cutoff },
            success: function (isExpired) {
                console.log(isExpired)
                if (isExpired == "True") {  // true คือ บัญชีหมดอายุแล้ว
                    swal({
                        title: "",
                        text: "บัญชี " + account_number + " หมดอายุแล้ว \n กรุณาตัดบัญชีรายได้ปัจจุบัน และสร้างบัญชีใหม่ก่อนอนุมัติการชำระเงินของผู้เรียน",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
                else {
                    ApprovePayment()
                }
            }
        });
    }

    //----------------------------------------------------------------

    function ApprovePayment() {

        $.ajax({
            type: "POST",
            url: "/ApprovePayment/ApprovePaymentOfOrder",
            data: {"order_Number": order_number },
            success: function (response) {
                if (response) {
                    CreateListIncomeAccountDetail()
                }
                else {
                    swal({
                        title: "",
                        text: "Something Wrong !",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            }
        });
    }

    //----------------------------------------------------------------

    function CreateListIncomeAccountDetail() {

        var income_account_detail_array = [] //1.สร้าง array ไว้เพื่อเก็บ object

        @foreach (var item in viewBagOrderCourseDetailList) {
            <text>

        var income_account_detail_obj = { //2.สร้าง object ไว้เพื่อเก็บค่าข้อมูลรายการบัญชีรายได้
            order_number: "",
            student_name: "",
            instructor_name: "",
            course_name: "",
            course_price: 0,
            service_charge: 0,
            transfer_status: false,
            transferer: "",
            datetime_transfer: "",
            account_number: "",
        }
            //3.นำค่าเข้าไปเก็บในแต่ละ Key ของ Object
            income_account_detail_obj.order_number = '@(item.order_Number)';
            income_account_detail_obj.student_name = student_name;
            income_account_detail_obj.instructor_name = '@(item.instructor_Name)';
            income_account_detail_obj.course_name = '@(item.course_Name)';
            income_account_detail_obj.course_price = '@(item.course_Price)';
            income_account_detail_obj.account_number = account_number;

            //4.เมื่อได้ค่าของแต่ละ Key ของ Object แล้วก็ Push ใส่ใน Array
            income_account_detail_array.push(income_account_detail_obj);
            </text>
        }

        $.ajax({
            type: "POST",
            url: "/ApprovePayment/CreateListIncomeAccountDetail",
            data: "{income_account_detail: " + JSON.stringify(income_account_detail_array) + "}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (response) {
                if (response == "True") {
                    swal({
                        title: "",
                        text: "อนุมัติชำระเงินเรียบร้อยแล้ว",
                        type: "success",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#35D664"
                    }, function () {
                            window.location.assign('/OrderCourseList/OrderCourseList');
                    });
                }
                else {
                    swal({
                        title: "",
                        text: "Something Wrong !",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            }
        });
    }


</script>