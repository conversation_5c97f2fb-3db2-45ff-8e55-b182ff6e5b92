﻿@*
    @model IEnumerable<SE_Project.Models.Course>
*@

@model SE_Project.Models.CourseListModel

@{
    ViewBag.Title = "Home Page";
}


@*<link href="~/Content/bootstrap-card.css" rel="stylesheet" />*@
@Scripts.Render("~/bundles/jqueryval")
<div style="margin-bottom:0px;"></div>

@*<nav class="navbar-primary">
        <ul class="navbar-primary-menu">
            <li>
                <a href="#"><span class="glyphicon glyphicon-list-alt"></span><span class="nav-label">Dashboard</span></a>
                <a href="#"><span class="glyphicon glyphicon-envelope"></span><span class="nav-label">Profile</span></a>
                <a href="#"><span class="glyphicon glyphicon-cog"></span><span class="nav-label">Settings</span></a>
                <a href="#"><span class="glyphicon glyphicon-film"></span><span class="nav-label">Notification</span></a>
                <a href="#"><span class="glyphicon glyphicon-calendar"></span><span class="nav-label">Monitor</span></a>
            </li>
        </ul>
    </nav>*@
<style>
</style>

<div class="col-lg-2" style="">
    <div style="margin-bottom:40px;"></div>

    <div style="margin-top:0%;">
        <div class="input-group add-on">
            <input class="form-control" placeholder="ค้นหาคอร์สเรียน" name="srch-term" id="srch-term" type="text">
            <div class="input-group-btn">
                <button class="btn btn-info" type="submit"><i class="glyphicon glyphicon-search"></i></button>
            </div>

        </div>
        <div class="card" style="margin-top:20px; height:300px;">
            <div class="card-header">
                <span style="font-size:16px;">หมวดหมู่คอร์สเรียน</span>
            </div>
            <div class="card-body">
                <div>
                    <div class=" form-group">
                        <label style="font-size:14px;"><input type="checkbox" />เขียนโปรแกรม</label>
                    </div>

                    <div class=" form-group">
                        <label style="font-size:14px;"><input type="checkbox" />เรียนติว</label>
                    </div>

                    <div class=" form-group">
                        <label style="font-size:14px;"><input type="checkbox" />เรียนภาษา</label>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <!-- <button class="btn btn-success" style="margin-top: 20px; margin-left: 20px" onclick="$('#course_form').submit();" data-toggle="modal" data-target="#show_course_modal">สั่งซื้อคอร์สเรียน</button> -->
            <button class="btn btn-success" id="order_course_button" style="margin-top: 20px; margin-left: 20px" onclick="CheckLogin()">สั่งซื้อคอร์สเรียน</button>
        </div>

    </div>
</div>

@*<form name="add_rma" id="add_rma">
        <table id='tbhold' class="table">
            <thead>
                <tr>
                    <th>Varenummer</th>
                    <th>Serienummer</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div class="col-md-8">
                            <input type="text" name="Varenummer[]" id="Varenummer" class="form-control" />
                        </div>
                    </td>
                    <td>
                        <div class="col-md-8">
                            <input type="text" name="Serienummer[]" id="Serienummer" class="form-control" />
                        </div>
                    </td>
                    <td>
                        <button type="button" name="add" id="add" class="btn btn-success">Add More</button>
                    </td>
            </tbody>
        </table>
        <input type="button" name="submit" id="submit" value="Submit" />
    </form>*@

@*<div class="col-lg-10">
    <form method="post">
        <div id="people-container">
            <h3>Person 1:</h3>
            <p>
                <label>First name</label><br>
                <input name="people[1][first_name]">
            </p>

            <p>
                <label>Last name</label><br>
                <input name="people[1][last_name]">
            </p>

            <p>
                <label>Email</label><br>
                <input name="people[1][email]">
            </p>

            <h3>Person 2:</h3>
            <p>
                <label>First name</label><br>
                <input name="people[2][first_name]">
            </p>

            <p>
                <label>Last name</label><br>
                <input name="people[2][last_name]">
            </p>

            <p>
                <label>Email</label><br>
                <input name="people[2][email]">
            </p>
        </div>

        <a href="javascript:;" id="add-new-person">Add new person</a>

        <p>
            <input type="submit">
        </p>
    </form>*@


@*<label for="chkPassport">
        <input type="checkbox" id="chkPassport"/>
    </label>
    Passport Number : <input type="text" id="txtPassport" value="" disabled="disabled"/>
    <h1>PlayList รายการคอร์สเรียน</h1>*@

@*<table class="table table-bordered">
        <thead>
            <tr>
                <th>เลือก</th>
                <th>โชว์</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i <= 5; i++)
            {
                <tr>

                    <td>
                        <label for="chkPassport">
                            <input type="checkbox" id="chkPassport" value="0" />
                        </label>
                    </td>
                    <td>
                        Passport Number : <input type="text" id="txtPassport" value="" disabled="disabled" />
                    </td>


                </tr>
            }
            </tbody>
    </table>*@
@*@for (int i = 0; i <= 16; i++)
    500x325
    {*@

<div class="col-lg-2">

</div>


<div class="col-lg-10">
    <a href="#">

        @using (Html.BeginForm(new { name = "course_form", id = "course_form" }))
        {

            for (int i = 0; i < Model.Courses.Count; i++)
            {
                <div class="col-lg-3 col-md-6 mb-4 container" style="margin-bottom:30px;">

                    <div class="card h-100" style="border-color:darkgray;">
                        @*<img class="card-img-top" src="~/image/scb.png" alt="">*@
                        @* <video class="card-img-top" src="~/Videofiles/txka_every end of the day karaoke.MP4"></video>*@
                        <video class="card-img-top"></video>

                        <div class="card-body">
                            <h5 class="card-title">@Model.Courses[i].course_Name</h5>
                            <p class="card-text">ผู้สอน : @Model.Courses[i].instructor_Name</p>
                        </div>
                        <div class="card-footer">
                            <span class="card-text" style="font-size:16px;">@Model.Courses[i].course_Price บาท</span>
                            <span>@Html.CheckBoxFor(model => model.Courses[i].isChecked, new { @class = "checkbox_of_each_course", value = @Model.Courses[i].ID })</span>
                        </div>
                    </div>
                </div>
            }
        }


    </a>
</div>

<script>

    var count = 0
    var course_id = []
    var courses_data

    //---------------------------------------------------------------------

    function CheckLogin() {
        $.ajax({
            type: "POST",
            url: "/Students/CheckLogin",
            success: function (isLogin) {

                if (isLogin == "False") {
                    swal({
                        title: "",
                        text: "กรุณา Log in ก่อน",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                } else {

                    count = count_checkbox()

                    if (count === 0) {
                        swal({
                            title: "",
                            text: "กรุณาเลือกคอร์สที่จะซื้อก่อน !",
                            type: "warning",
                            confirmButtonText: "ตกลง",
                            confirmButtonColor: "#d95940"
                        });
                    } else {

                        CheckLastOrderStatus()

                    }
                }
            }
        });
    }

    //---------------------------------------------------------------------

    function count_checkbox() {

        var inputs = document.getElementsByTagName("input"); //get Element by class
        var checkboxes = []; //เก็บ Checkboxes ทั้งหมดของหน้า Index
        var checked = []; //เก็บ Checkboxes ที่ติ๊กถูก
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].type == "checkbox") {
                checkboxes.push(inputs[i]);
                if (inputs[i].checked) {
                    checked.push(inputs[i]);
                }
            }
        }

        var count_chb = checked.length; //number of checked checkboxes

        return count_chb;
    }

    //---------------------------------------------------------------------

    function CheckLastOrderStatus() {

        $.ajax({
            type: "POST",
            url: "/Students/CheckLastOrderStatus",
            success: function (last_order_status) {
                if (last_order_status == 1) {  //1 คือ รอชำระเงิน (ซื้อคอร์สแล้วยังไม่ได้แจ้งชำระเงิน)
                    swal({
                        title: "",
                        text: "กรุณาชำระเงินจากออเดอร์ที่ซื้อไว้ก่อน",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
                else if (last_order_status == 2) { //2 Pending คือ รอตรวจชำระเงิน (ซื้อคอร์สแล้ว แจ้งชำระเงินแล้ว รอ Admin มาอนุมัติ)
                    swal({
                        title: "",
                        text: "ผู้ดูแลระบบกำลังตรวจสอบการชำระเงินของผู้ใช้",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                } else {

                    GetIdFromCheckboxes()

                }
            }
        });
    }


    //---------------------------------------------------------------------


    function GetIdFromCheckboxes() {

        course_id.length = 0 //clear array

        $('.checkbox_of_each_course:checked').each(function () {
            course_id.push(this.value)
        });

        GetCourseData()

    }

    //---------------------------------------------------------------------

    function GetCourseData() {

        $.ajax({
            type: "POST",
            url: "/Students/GetCourseData",
            data: "{course_id: " + JSON.stringify(course_id) + "}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (get_courses_data) {
                courses_data = get_courses_data
                showCoursesDataInModal(courses_data)
            },
            error: function () {
                swal({
                    title: "",
                    text: "Something Wrong !",
                    type: "warning",
                    confirmButtonText: "ตกลง",
                    confirmButtonColor: "#d95940"
                });
            }
        });
    }

    //---------------------------------------------------------------------

    function showCoursesDataInModal(courses_data) {

        var total_price = 0
        var total_course = 0

        $(document).ready(function () {

            var tbody = document.getElementById("course_tbody")

            for (var i = 0; i < courses_data.length; i++) {
                //เพิ่มแถว
                var tr = tbody.insertRow();

                //เพิ่มข้อมูลลงไปใน Cell
                var td = tr.insertCell();
                td.innerHTML = i + 1

                var td2 = tr.insertCell();
                td2.innerHTML = courses_data[i].course_Name

                var td3 = tr.insertCell();
                td3.innerHTML = courses_data[i].instructor_Name

                var td4 = tr.insertCell();
                td4.innerHTML = courses_data[i].course_Price

                total_price = total_price + courses_data[i].course_Price
            }
            total_course = courses_data.length

            $(tbody).append('<tr>' + '<td colspan="2">' + "จำนวนคอร์สที่สั่งซื้อ " + total_course + " คอร์ส" + '</td>' +
                '<td colspan="2">' + "ราคารวม " + total_price + " บาท" + '</td>' + '</tr>')

            $('#show_course_list_modal').modal()

            $('#show_course_list_modal').on('hide.bs.modal', function (e) {
                $('#course_tbody').empty()
            })


        })
    }

    //---------------------------------------------------------------------

    function acceptOrderCourse() {
        
        $.ajax({
            type: "POST",
            url: "/Students/CreateOrderCourse",
            data: "{courses_data: " + JSON.stringify(courses_data) + "}",
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            success: function (order_Number) {
                if (order_Number) {
                    swal({
                        title: "",
                        text: "สั่งซื้อคอร์สสำเร็จ \n" + "Order Number : " + order_Number,
                        type: "success",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#35D664"
                    });
                } else {
                    swal({
                        title: "",
                        text: "Something Wrong !",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            },
            error: function (response) {
                swal({
                    title: "",
                    text: "Something Wrong ! \n " + "Response : " + response,
                    type: "warning",
                    confirmButtonText: "ตกลง",
                    confirmButtonColor: "#d95940"
                });
            }
        });
            
    }

</script>


<!-- Modal -->
<div class="modal fade" id="show_course_list_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">รายการคอร์สเรียนที่สั่งซื้อ</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered" id="course_table">

                    <thead id="course_thead">
                        <tr style="text-align: center">
                            <th>ลำดับ</th>
                            <th>ชื่อคอร์ส</th>
                            <th>ชื่อผู้สอน</th>
                            <th>ราคา</th>
                        </tr>
                    </thead>

                    <tbody id="course_tbody">
                    </tbody>

                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-dismiss="modal" onclick="acceptOrderCourse()">ยืนยันการสั่งซื้อ</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
<!-- End Modal -->
@*}*@
<br />
<!-- /.row -->
@*===================== Log in Modal ==================*@

@*@using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="modal fade" id="Login_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h1 class="modal-title" style="text-align: center;"><i class="fa fa-user-circle" style="font-size:40px;"></i>&nbsp;Login</h1>
                    </div>
                    <div class="modal-body" style="height:115px;">

                        <div class="form-group col-md-12">
                            <div class="col-md-2">
                                <label>Email<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                            </div>
                            <div class="col-md-10">

                                @Html.EditorFor(model => model.UserEmail, new { htmlAttributes = new { @placeholder = "Email", @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.UserEmail, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group col-md-12">
                            <div class="col-md-2 ">
                                <label>Password<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
                            </div>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.UserPassword, new { htmlAttributes = new { @placeholder = "Password", @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.UserPassword, "", new { @class = "text-danger" })
                            </div>
                        </div>

                    </div>


                    <div class="modal-footer">
                        <div class="col-md-offset-2 col-md-10" style="text-align:right;">
                            <button type="submit" class="btn btn-success"><i class="glyphicon glyphicon-log-in"></i>&nbsp;เข้าสู่ระบบ</button>
                            <a href="" class="btn btn-danger"><i class="glyphicon glyphicon-refresh"></i>&nbsp;ลืมรหัสผ่าน</a>
                        </div>
                    </div>

                </div>
            </div>
        </div>*@

@*=====================================================*@

@section Scripts {
    @*@Scripts.Render("~/bundles/jqueryval")*@

    @*<script src="~/Scripts/jquery-3.3.1.min.js"></script>*@

}


<script src="~/Scripts/jquery-3.3.1.min.js"></script>
<script>
    $(function () {
        @*var ay = @Html.Raw(Json.Encode());*@

        $("#chkPassport").click(function () {
            if ($(this).is(":checked")) {
                $("#txtPassport").removeAttr("disabled");
                $("#txtPassport").focus();
                $("#txtPassport").val('valueDefault');

            }
            else {
                $("#txtPassport").attr("disabled", "disabled");
                $("#txtPassport").val('');
            }
        });
    });

        @ViewBag.js

        function ErrorMessage()
        {
            swal({
                title: "",
                text: "Email หรือ Password ไม่ถูกต้อง !!!",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940"
                //showConfirmButton: false
            }//, function () {
                //window.location.assign('Index.aspx');
                //}
            );
        }
</script>