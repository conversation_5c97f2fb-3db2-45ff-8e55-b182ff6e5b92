﻿@model SE_Project.Models.Course

@*@model IList<SE_Project.Models.Lecture>*@


@* @model IEnumerable<Project_COS4105.Models.Course>   *@

@{
    ViewBag.Title = "CreateCourse";

}

@{

}
<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-facetime-video"></i>&nbsp;สร้างคอร์สเรียน</p>
</div>

@*<form id="createCourse_Form">*@


@using (Html.BeginForm("CreateCourse", "CreateCourse", FormMethod.Post, new { enctype = "multipart/form-data" @*,onsubmit="return validateForm(event)"*@}))
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal well col-md-12">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div style="margin-top:10px;"></div>
        <div class="form-group">

            @*<input id="asd" class="form-control"/>*@
            <div class="col-md-3 control-label">
                <label>เลือกหมวดหมู่<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.DropDownListFor(model => model.category_ID, ViewBag.CategoryList as SelectList, "-- เลือกหมวดหมู่ --", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.category_ID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-3 control-label">
                <label>ชื่อคอร์ส<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model => model.course_Name, new { htmlAttributes = new { @placeholder = "ชื่อคอร์ส", @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.course_Name, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">ราคา<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model => model.course_Price, new { htmlAttributes = new { @placeholder = "ราคา", @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.course_Price, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3 control-label">
                <label class="col-form-label">รายละเอียด<i style="color: red; font-size: 20px;">*</i>&nbsp;:</label>
            </div>
            <div class="col-md-9">
                @Html.TextAreaFor(model => model.course_Description, new { @placeholder = "รายละเอียด คอร์ส", @class = "form-control" })
                @Html.ValidationMessageFor(model => model.course_Description, "", new { @class = "text-danger" })
            </div>

        </div>

        <hr />
        @{
            int numLec = 1;

            for (int i = 0; i < 8; i++)
            {

                //numLec = 1;
                <div class="form-group control-label">
                    <div class="col-md-2 control-label">
                        <label class="col-form-label">Lecture @numLec<i style=" font-size: 20px;"></i>&nbsp;:</label>

                    </div>
                    <div class="col-md-5 control-label">

                        @*@Html.EditorFor(model => model.Lecture.lecture_Name[i], new { htmlAttributes = new { @placeholder = "ชื่อ Lecture", @class = "form-control", name = "lecture[i]" } })*@
                        @Html.Editor("[" + i + "].lecture_Name", new { htmlAttributes = new { @placeholder = "ชื่อ Lecture", @class = "form-control" ,@id= i + "_lecture_id" } })
                        @*@Html.ValidationMessageFor(model => model.Lecture.lecture_Name, "", new { @class = "text-danger" })*@
                    </div>

                    <div class="col-md-2 control-label">
                        <label class="col-form-label">เลือกไฟล์ที่ @numLec<i style=" font-size: 20px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-3 control-label">

                        @*@Html.TextBox("videofile[" + i + "].videoFile_Name", null, new { type = "file", @class = "form-control" })*@

                        
                        @Html.Editor("[" + i + "].videoFile_Name", new { htmlAttributes = new { type = "file",@multiple = "multiple" ,@class = "form-control" } })
                        @*@Html.Editor("[" + i + "].videoFile_Name", new { htmlAttributes = new {type="file" ,@class = "form-control" } })*@
                        @*<input type="file" name="files[i]" class="form-control" />*@


                    </div>
                </div>
                

                <div class="form-group control-label">

                    <div class="col-md-2">
                        <label class="col-form-label">Description @numLec<i style=" font-size: 20px;"></i>&nbsp;:</label>
                    </div>
                    <div class="col-md-10 text-left">
                        @Html.TextArea("[" + i + "].lecture_Description", new { @placeholder = "ราบละเอียด Lecture", @class = "form-control" })
                        @*@Html.ValidationMessageFor(model => model.Lecture.lecture_Description, "", new { @class = "text-danger" })*@
                        @*@Html.TextAreaFor(model => model.Lecture.lecture_Description[i], new { @placeholder = "ราบละเอียด Lecture", @class = "form-control" })*@

                    </div>
                </div>
                <hr />
                
                numLec++;
            }
        }



        <hr />
        <div class="form-group">
            <div style="text-align:center;">
                <button class="btn btn-success" type="submit"><i class="glyphicon glyphicon-save"></i>&nbsp;บันทึก</button>
            </div>
        </div>

    </div>

}
@*</form>*@

<script>
    function validateForm(event) {
        for (var i = 0; i < 10; i++) {
            var check_id = i + "_lecture_id";
            var check_value = $("#" + check_id).val();
            if (check_value == "") {
                alert("กรุณากรอก Lecture ที่ " + (i + 1));
                return false;
            }
        }
        return true;
    }
    //alert("ห้ามกรอกข้าม");
    //var a1 = "2000";
    //var b1 = parseInt(a1);
    //var a2 = "3000";
    //var b2 = parseInt(a2);
    //var a3 = b1+b2;
    //$("#asd").val(a3);

    @ViewBag.message
    function SuccessMessages() {
        swal({
            title: " ",
            text: "บันทึกข้อมูลคอร์สสำเร็จ",
            type: "success",
            showConfirmButton: true
        }, function () {
            window.location.assign('/Home/InstructorMainPage');
        });
    }
    function ErrorMessages() {
        swal({
            title: "",
            text: "ชื่อคอร์เรียนซ้ำกันในระบบ !!",
            type: "warning",
            confirmButtonText: "ตกลง",
            confirmButtonColor: "#d95940"
        });
    }

</script>
