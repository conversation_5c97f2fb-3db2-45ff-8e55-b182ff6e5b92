﻿@model SE_Project.Models.OrderCourse

@{
    ViewBag.Title = "PaymentNotify";
    var viewBag_data = ViewBag.last_order;
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;แจ้งชำระเงิน</p>
</div>

@using (Html.BeginForm("PaymentNotify", "PaymentNotify", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:20px;">Order Number : </span>
            </div>
            <div class="col-sm-5" style="font-size:20px;">
                @if (viewBag_data == null || viewBag_data.orderStatus_ID == 2 || viewBag_data.orderStatus_ID == 3)
                {
                    <span style="font-size:20px;">-</span>
                }
                else
                {
                    <span style="font-size:20px;">@viewBag_data.order_Number</span>
                }
            </div>
        </div>

        <div class="form-group col-sm-12">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:20px;">จำนวนเงินที่ต้องโอน :</span>
            </div>
            <div class="col-sm-3">

                @if (viewBag_data == null || viewBag_data.orderStatus_ID == 2 || viewBag_data.orderStatus_ID == 3)
                {
                    <span style="font-size:20px;">-</span>
                }
                else
                {
                    <span style="font-size:20px;">@viewBag_data.total_Price บาท</span>
                }
            </div>
        </div>

        <div class="form-group col-sm-12" style="margin-top:20px;">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:20px;">หมายเลขบัญชี : </span>
            </div>
            <div class="col-sm-6">
                <span style="font-size:20px;"><img src="~/image/kasikorn.png" width="35" />&nbsp; ธนาคารกสิกรไทย (499-2-54321-1)</span>
            </div>
        </div>

        <div class="form-group col-sm-12" style="margin-top:20px;">
            <div class="col-sm-3" style="text-align: right;">
                <span style="font-size:20px;">สลิปหลักฐานการโอนเงิน : </span>
            </div>
            <div class="col-sm-3">
                <input type="file" name="ImageFile" required />
            </div>
        </div>

        <hr style="color:blueviolet;" />
        <div class="col-md-offset-2 col-md-10" style="text-align:center;">
            @if (viewBag_data == null || viewBag_data.orderStatus_ID == 2 || viewBag_data.orderStatus_ID == 3)
            {
                <input type="submit" value="แจ้งชำระเงิน" class="btn btn-default" style="font-size: 18px" ; margin-top: 30px; disabled />
            }
            else
            {
                <input type="submit" value="แจ้งชำระเงิน" class="btn btn-success" style="font-size: 18px" ; margin-top: 30px; />
            }
        </div>


    </div>
}

@if (ViewBag.showSwal != null)
{
    <script>
    swal({
        title: "",
        text: "แจ้งชำระเงินสำเร็จ \n กรุณารอตรวจสอบการชำระเงินจากผู้ดูแลระบบ",
        type: "success",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#35D664"
    });
    </script>
}


