﻿
<p id="GFG_UP">
</p>

<h2 id="bnk48"></h2>

เขียนโปรแกรม: <input type="checkbox" name="type" value="prg" /> ,
เรียนภาษา: <input type="checkbox" name="type" value="lang" /> ,
เรียนติว: <input type="checkbox" name="type" value="tutor" />

<br>
<button>click here</button>
<p id="GFG_DOWN">

</p>


@Scripts.Render("~/bundles/jqueryval")
@Scripts.Render("~/bundles/jquery")
<script>
    $('#GFG_UP')
        .text('First check few elements then click on the button.');

    //$('#bnk48').text('gfifyfufygfuihuihogfu');

    $('button').on('click', function () {
        var category = [];
        $("input:checked").each(function () {
            category.push($(this).val());
        });

        $.ajax({
            type: "POST",
            url: "/Admin/TestCheckbox",
            data: { "category": category },

            success: function () {
                window.location.assign("/Admin/TestCheckbox");
            }
        });
        //$('#GFG_DOWN').text(array);

        //alert(array);
    });
</script>


@*@for (int i = 0; i < Model.Categories.Count; i++)
    {
        @Html.CheckBoxFor(model => model.Categories[i].IsChecked)
        <label>@Model.Categories[i].category_Name</label>

    }*@