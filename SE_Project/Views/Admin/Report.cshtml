﻿
@{
    ViewBag.Title = "Report";
}
<style>
</style>

<div class="content">
    <div class="alert-info">
        <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;Report</p>
    </div>
    <div style="margin-top:30px;"></div>



    <div class="form-group container well col-sm-12">
        <div class="col-sm-3 " style="text-align: right;">
            <span style="font-size:22px;">ประเภทการรายงาน :</span>
        </div>
        <div class="col-sm-3 col-lg-offset-0">
            <select class="form-control" id="type_report" style="height: 40px; font-size:16px;">
                <option class="form-control">-- เลือกประเภทการรายงาน --</option>
                <option class="form-control" style="font-size:16px;" value="1">คอร์สเรียน</option>
                <option class="form-control" style="font-size:16px;" value="2">ผู้สอน</option>
                <option class="form-control" style="font-size:16px;" value="3">ผู้เรียน</option>
            </select>
        </div>
        <div class="col-sm-2">
            <button class="btn btn-success text-right" type="button" id="btnViewReport" style="font-size:18px;"><i class="glyphicon glyphicon-search"></i>&nbsp;ดูรายงาน</button>
        </div>
    </div>
</div>


@*<div class="loading">
        <h4>กรุณารอสักครู่</h4>
        <img src="~/image/Loading.gif" style="width: 120px;" />
    </div>*@


@Scripts.Render("~/bundles/jqueryval")
@Scripts.Render("~/bundles/jquery")

<script>
    $(document).ready(function () {
        $("#").click(function () {

        });
    });

    $(document).ready(function () {
        $("#btnViewReport").click(function () {

            var report_type = $("#type_report").val();

            if (report_type == "1") {
                window.location.assign('/Admin/CourseReport');
            }
            else if (report_type == "2") {
                window.location.assign('/Admin/InstructorReport');
            }
            else if (report_type == "3") {
                window.location.assign('/Admin/StudentReport');
            }
            else {
                swal({
                    title: "",
                    text: "กรุณาเลือก ประเภทการรายงาน !!",
                    type: "warning",
                    confirmButtonText: "ตกลง",
                    confirmButtonColor: "#d95940"
                    //showConfirmButton: false
                });
            }

        });
    });


</script>