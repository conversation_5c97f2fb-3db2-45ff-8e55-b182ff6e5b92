﻿
@{
    ViewBag.Title = "StudentReportDetail";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;<a href="~/Admin/Report">Report</a> > <a href="~/Admin/StudentReport">Report เกี่ยวกับผู้เรียน</a> > คอร์สเรียนที่สั่งซื้อ</p>
</div>

<div style="margin-top:30px;"></div>

<div class="container well col-sm-12">
    <div class="" style="margin-top: 0px;">
        <table class="table table-striped table-bordered table-hover" id="">
            <thead>
                <tr style="background-color:#299824; color:white;">
                    <th class="text-center" style="width: 5%;"># ลำดับ</th>
                    <th style="width: 48%; ">ชื่อคอร์ส</th>
                    <th class="text-center" style="width: 8%; ">วันที่สั่งซื้อคอร์ส</th>
                </tr>

            </thead>
            <tbody>
                <tr>
                    <td class="text-center">1</td>
                    <td>C#.net MVC</td>
                    <td class="text-center">10 ม.ค.2562</td>
                </tr>
            </tbody>
        </table>

    </div>

</div>