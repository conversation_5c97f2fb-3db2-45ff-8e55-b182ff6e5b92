﻿
@{
    ViewBag.Title = "InstructorReport";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;<a href="~/Admin/Report">Report</a> > Report เกี่ยวกับผู้สอน</p>
</div>

<div style="margin-top:30px;"></div>

<div class="container well col-sm-12">
    <div class="" style="margin-top: 0px;">
        <table class="table table-striped table-bordered table-hover" id="">
            <thead>
                <tr style="background-color:#0094ff; color:white;">
                    <th class="text-center" style="width: 5%;"># ลำดับ</th>
                    <th style="width: 48%; ">ชื่อผู้สอน</th>
                    <th class="text-center" style="width: 8%; ">จำนวน คอร์ส</th>
                    <th class="text-center" style="width: 7%; ">ดูรายละเอียด</th>
                </tr>

            </thead>
            <tbody>
                <tr>
                    <td class="text-center">1</td>
                    <td>นาย เบิร์ด รักราม</td>
                    <td class="text-center">10</td>
                    <td class="text-center"><a href='@Url.Action("InstructorReportDetail", "Admin", new { })' class="btn btn-primary" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ดูรายละเอียด</b></a></td>
                </tr>
            </tbody>
        </table>

    </div>

</div>