﻿
@{
    ViewBag.Title = "CourseReport";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;<a href="~/Admin/Report">Report</a> > Report เกี่ยวกับคอร์สเรียน</p>
</div>

<div style="margin-top:30px;"></div>

<div class="form-group container ">
    <div class="input-group add-on col-sm-3">
        <input class="form-control" placeholder="กรอกชื่อคอร์สเรียน" name="" id="" type="text">
        @*@Html.EditorFor(model => model.EducationMastor, new { htmlAttributes = new { @placeholder = "กรอกชื่อคอร์สเรียน", @class = "form-control", } })*@
        <div class="input-group-btn ">
            <button class="btn btn-info text-right" type="submit"><i class="glyphicon glyphicon-search"></i>&nbsp;ค้นหา</button>
        </div>
    </div>
</div>

<div class="container well col-sm-12">
        <div class="row">
            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:18px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;คอร์สเรียนทั้งหมด
                </label>
            </div>
            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:18px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;เขียนโปรแกรม
                </label>
            </div>
            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:18px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;เรียนภาษา
                </label>
            </div>
            <div class="col-sm-3">
                <label class="checkbox-inline" style="font-size:18px;">
                    <input type="checkbox" value="" style="width:16px; height:23px;">&nbsp;ติวสอบ
                </label>
            </div>
        </div>
</div>

<div class="container well col-sm-12">
    <div class="" style="margin-top: 0px;">
        <table class="table table-striped table-bordered table-hover" id="">
            <thead>
                <tr style="background-color:#9933CC; color:white;">
                    <th style="width: 30%; ">ชื่อคอร์ส</th>
                    <th class="text-center" style="width: 6%; ">ราคา</th>
                    <th class="text-center" style="width: 8%; ">จำนวน Order</th>
                    <th class="text-center" style="width: 7%; ">ยอดขายรวม</th>
                </tr>

            </thead>
            <tbody>
                <tr>
                    <td>aa</td>
                    <td class="text-center">ss</td>
                    <td class="text-center">dd</td>
                    <td class="text-center">ff</td>
                </tr>
                


            </tbody>
        </table>
        <div class="text-right" style="font-size: 18px;"><b>Total : </b> 00000 บาท</div>
        
    </div>
   
</div>