﻿
@{
    ViewBag.Title = "ApprovePayment";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class=""></i>&nbsp;อนุมัติการชำระเงิน</p>
    
</div>
<div style="margin-top:30px;"></div>
<div class="from-group col-md-12">
    <div class="card">
        <div class="card-header">
            <span style="font-size:18px;"><u><b>ข้อมูลการโอนเงิน</b></u></span>
        </div>
        <div class="card-body">
            <div style="font-size:18px;">
                <div class="col-lg-12 form-group">
                    <div class="col-lg-6">รหัสใบสั่งซื้อคอร์สเรียน : </div>
                    <div class="col-lg-6">วันเวลา : </div>
                </div>

                <div class="col-lg-12 form-group">
                    <div class="col-lg-6">ชื่อคอร์สเรียน : </div>
                    <div class="col-lg-6">ราคาคอร์สเรียน : </div>
                </div>

                <div class="col-lg-12 form-group">
                    <div class="col-lg-6">ชื่อผู้สอน : </div>
                    <div class="col-lg-6"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="from-group col-md-12" style="margin-top: 5px;">
    <div class="card">
        <div class="card-header">
            <span style="font-size:18px;"><u><b>ข้อมูลลูกค้า</b></u></span>
        </div>
        <div class="card-body">
            <div style="font-size:18px;">
                <div class="col-lg-12 form-group">
                    <div class="col-lg-6">ชื่อสกุล : </div>
                    <div class="col-lg-6">Email : </div>
                </div>

                <div class="col-lg-12 form-group">
                    @*<div class="col-lg-6">Email : </div>
                    <div class="col-lg-6">ที่อยู่ : </div>*@
                </div>

            </div>
        </div>
    </div>
</div>

<div class="container col-sm-6 form-group">

    @*<img src="~/image/kasikorn.png" width="100" />*@
</div>
<div class="col-sm-12">
    <h2>ภาพสลิป</h2>
</div>
<div class="col-sm-12">
    <div class="col-sm-2 form-group"><button class="btn-success btn-lg" id="btnApprove" type="button">อนุมัติชำระเงิน</button></div>
    <div class="col-sm-3 form-group"><button class="btn-danger btn-lg" id="btnNotApprove" type="button">ไม่อนุมัติชำระเงิน</button></div>
    <div class="col-sm-7"></div>
</div>

@*====================================================== Pop-up อนุมัติใบสั่งซื้อ =======================================================*@
<div class="modal fade" id="Approve_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">
    <div class="modal-dialog">

        <div class="modal-content ">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title alert-success" style="text-align:center;">&nbsp;อนุมัติใบสั่งซื้อคอร์สเรียนแล้ว</h3>
            </div>

            <div style="text-align:center; margin-top: 10px; height:230px;" class="">
                <span style="font-size:18px;"><u><b>รายละเอียดใบสั่งซื้อคอร์สเรียน</b></u></span>

                <div style="font-size:16px; margin-top:15px;" class="text-left">
                    <div class="col-lg-12 form-group">
                        <div>ชื่อคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>รหัสใบสั่งซื้อคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>ราคาคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>ชื่อผู้สอน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>วันและเวลาที่อนุมัติชำระเงิน : </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="text-align:center;">
                <a href="#" class="btn btn-success" style="font-size: 20px;">ตกลง</a>               
            </div>
        </div>
    </div>
</div>
@*====================================================== Pop-up ไม่อนุมัติใบสั่งซื้อ =======================================================*@
<div class="modal fade" id="NotApprove_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content ">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title alert-danger" style="text-align:center;">&nbsp;ไม่อนุมัติใบสั่งซื้อคอร์สเรียน !!</h3>
            </div>
            <div style="text-align:center; margin-top: 10px; height:230px;" class="">
                <span style="font-size:18px;"><u><b>รายละเอียดใบสั่งซื้อคอร์สเรียน</b></u></span>

                <div style="font-size:16px; margin-top:15px;" class="text-left">
                    <div class="col-lg-12 form-group">
                        <div>ชื่อคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>รหัสใบสั่งซื้อคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>ราคาคอร์สเรียน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>ชื่อผู้สอน : </div>
                    </div>
                    <div class="col-lg-12 form-group">
                        <div>วันและเวลาที่ไม่อนุมัติชำระเงิน : </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="text-align:center;">
                <a href="#" class="btn btn-danger" style="font-size: 20px;">ตกลง</a>
            </div>
        </div>

    </div>
</div>
@Scripts.Render("~/bundles/jqueryval")
@Scripts.Render("~/bundles/jquery")
@Scripts.Render("~/bundles/bootstrap")
@*<script src="~/Scripts/jquery-3.3.1.min.js"></script>*@
<script>
    $(document).ready(function () {
        $("#btnApprove").click(function () {
            $('#Approve_Modal').modal();
            //alert("App");
        });
    });

    $(document).ready(function () {
        $("#btnNotApprove").click(function () {
            $('#NotApprove_Modal').modal();
            //alert("App");
        });
    });
</script>