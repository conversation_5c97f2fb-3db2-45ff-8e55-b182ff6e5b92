﻿@model IEnumerable<SE_Project.Models.IncomeAccount>

@{
    ViewBag.Title = "ManageIncomeAccount";
}

<div class="col-sm-12">
    <p class="alert-info" style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;จัดการบัญชีรายได้</p>
</div>

<div style="margin-top:60px;"></div>

<div class="col-sm-12">
    <div style="text-align: right;">

        <button class="btn btn-success" onclick="CheckLastAccountStatus()" data-toggle="modal" data-target="#myModal">สร้างบัญชี</button>

    </div>
    <div style="margin-bottom:30px;"></div>

</div>

<!------------------------------------ส่วนตารางแสดงรายการบัญชี------------------------------------>
<div class="col-sm-12">
    <div class="well">
        <table class="table table-striped table-bordered table-hover" id="orderCourseList">
            <thead>
                <tr style="background-color:#64b5f6;">
                    <th style="width: 10%; text-align:center">ลำดับ</th>
                    <th style="width: 19%; text-align:center">หมายเลขบัญชี</th>
                    <th style="width: 24%; text-align:center">สถานะบัญชี</th>
                    <th style="width: 14%; text-align:center">ดูรายละเอียด</th>
                    <th style="width: 7%; text-align:center">ตัดบัญชี</th>
                    <th style="width: 7%; text-align:center">ผู้ตัดบัญชี</th>
                    <th style="width: 16%; text-align:center">วันที่สร้างบัญชี</th>
                    <th style="width: 16%; text-align:center">วันที่ตัดบัญชี</th>
                </tr>
            </thead>

            <tbody>
                @{
                    int i = 1;
                    foreach (var IncomeAccountLists in Model)
                    {
                                <tr class="text-center">
                                    <td>@i</td>
                                    <td>@IncomeAccountLists.account_number</td>
                                    <td>@SE_Project.Helper.Utility.ConvertIncomeAccountStatus(@IncomeAccountLists.account_status)</td>
                                    <td>
                                        <a href='@Url.Action("ManageIncomeForInstructor", "ManageIncomeForInstructor",
                                            new { account_number = IncomeAccountLists.account_number, isLastAccount = false})'
                                           class="btn btn-warning" style="font-size:12px;">
                                            <i class="glyphicon glyphicon-search"></i>&nbsp;<b>ดูรายละเอียด</b>
                                        </a>
                                    </td>

                                    <td>
                                        @if (IncomeAccountLists.account_status == true)
                                        {
                                           
                                        <button type="button" class="btn btn-danger Btn_cutoffAccount" id="@IncomeAccountLists.account_number" onclick="cufoffAccount()">
                                            <i class="glyphicon glyphicon-remove"></i>&nbsp;<b>ตัดบัญชี</b>
                                        </button>

                                        }
                                        else
                                        {
                                            <button class="btn btn-default" disabled>
                                                <i class="glyphicon glyphicon-remove"></i>&nbsp;<b>ตัดบัญชี</b>
                                            </button>
                                        }
                                    </td>

                                    <td>
                                        @if (@IncomeAccountLists.cutoffer_name == null)
                                        {
                                            <p>-</p>
                                        }
                                        else
                                        {
                                            <p>@IncomeAccountLists.cutoffer_name</p>
                                        }
                                    </td>

                                    <td>@IncomeAccountLists.create_date</td>
                                    <td>@IncomeAccountLists.cutoff_date</td>

                                </tr>

                        i++;

                    }
                }

            </tbody>
        </table>
    </div>
</div>
<!------------------------------------ส่วนตารางแสดงรายการบัญชี------------------------------------>

<!-- Modal -->
<div class="modal fade" id="modal_create_acoount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">

        <div class="modal-content">

            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">เลือกวันที่เริ่มต้น / วันที่สิ้นสุดบัญชี</h4>
            </div>

            <div class="modal-body">
                <div class="col-md-12">
                    <div class="row">
                            <div class="form-group">
                                <label>วันที่เริ่มต้นบัญชี : </label>
                                <input type="text" id="currentDate" disabled />
                            </div>

                            <div class="form-group">
                                <label>วันที่สิ้นสุดบัญชี : </label>
                                <input type="text" id="select_datepicker" required />
                            </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-dismiss="modal" onclick="createIncomeAccount()">ยืนยัน</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>

            <!-- /.modal-content -->
        </div>

        <!-- /.modal-dialog -->
    </div>

    <!-- /.modal -->
</div>


<script>

    $(function () {
        $("#select_datepicker").datepicker({
            changeYear: true,
            changeMonth: true,
            minDate: '+1d', //ไม่ให้เลือกวันที่ย้อนหลังจากวันที่ปัจจุบัน
            dateFormat: 'dd/mm/yy'
        });
    });

    //-------------------------------------------

    function showModal_DatePicker() {
        $(document).ready(function () {
            $('#modal_create_acoount').modal()
        })

        getCurrentDate()

    }

    //--------------------------------------------

    function getCurrentDate() {
        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1;
        var yyyy = today.getFullYear();
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (mm < 10) {
            mm = '0' + mm;
        }
        today = dd + '/' + mm + '/' + yyyy;
        document.getElementById("currentDate").value = today;
    }

    //--------------------------------------------

    function CheckLastAccountStatus() {

        $.ajax({
            type: "POST",
            url: "/ManageIncomeAccount/CheckLastAccountStatus",
            success: function (last_account_status) {
                if (last_account_status == "False") { //ถ้าไม่มีบัญชีไหนมีสถานะเป็น "Active" อยู่ ก็ให้สามารถสร้างบัญชีใหม่ได้
                    showModal_DatePicker()
                } else {
                    swal({
                        title: "",
                        text: "ไม่สามารถสร้างบัญชีใหม่ได้ \nกรุณาตัดบัญชีที่ใช้อยู่ก่อนสร้างบัญชีใหม่",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            },
        });
    }

    function createIncomeAccount() {

        var start_date = document.getElementById("currentDate").value;
        var end_date = document.getElementById("select_datepicker").value;
 
        $.ajax({
            type: "POST",
            url: "/ManageIncomeAccount/CreateIncomeAccount",
            data: { 'start_date': start_date, 'end_date': end_date },
            success: function (response) {
                if (response == "True") {
                    swal({
                        title: "",
                        text: "สร้างบัญชีรายได้สำเร็จ",
                        type: "success",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#35D664"
                    }, function () {
                        window.location.assign('/ManageIncomeAccount/ManageIncomeAccount');
                    });
                } else {
                    swal({
                        title: "",
                        text: "Something Wrong !",
                        type: "warning",
                        confirmButtonText: "ตกลง",
                        confirmButtonColor: "#d95940"
                    });
                }
            },
        });
    }

    //--------------------------------------------

    function cufoffAccount() {

        var account_number = $(".Btn_cutoffAccount").attr("id");
        
        swal({
            title: "",
            text: "คุณต้องตัดบัญชีรายได้ใช่หรือไม่ ?",
            type: "warning",
            confirmButtonText: "ตกลง",
            confirmButtonColor: "#d95940",
            showConfirmButton: true,
            showCancelButton: true
        }, function () {
            $.ajax({
                type: "POST",
                url: "/ManageIncomeAccount/CutOffIncomeAccount",
                data: { 'account_number': account_number },
                success: function (response) {
                    if (response == "True") {
                        swal({
                            title: "",
                            text: "ตัดบัญชีรายได้สำเร็จ",
                            type: "success",
                            confirmButtonText: "ตกลง",
                            confirmButtonColor: "#35D664"
                        }, function () {
                            window.location.assign('/ManageIncomeAccount/ManageIncomeAccount');
                        });
                    } else {
                        swal({
                            title: "",
                            text: "Something Wrong !",
                            type: "warning",
                            confirmButtonText: "ตกลง",
                            confirmButtonColor: "#d95940"
                        });
                    }
                }
            });
        });

    }

</script>
