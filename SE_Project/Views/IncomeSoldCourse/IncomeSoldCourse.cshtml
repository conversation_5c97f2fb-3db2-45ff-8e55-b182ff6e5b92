﻿@model IEnumerable<SE_Project.Models.SoldCourse>

@{
    ViewBag.Title = "IncomeSoldCourse";
    int total_course = 0;
    double total_income = 0;
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;รายได้จากการขายคอร์ส</p>
</div>

<div class="well" style="margin-top: 20px;">
    <table class="table table-striped table-bordered table-hover" id="example">
        <thead>
            <tr style="background-color:deepskyblue;">
                <th style="text-align:center;">ลำดับ</th>
                <th style="">ชื่อคอร์ส</th>
                <th style="">ราคา</th>
                <th style="">จำนวนยอดขาย</th>
                <th>ราคา x จำนวนยอดขาย</th>
                <th>หักค่าบริการ</th>
                <th>จำนวนแงินที่ได้รับ</th>
                <th>รายละเอียดผู้เรียน</th>
            </tr>

        </thead>
        <tbody>

            @{
                int num = 1;
                foreach (var incomeSoldCourse_item in Model)
                {
                    <tr>
                        <td style="text-align:center;">@num</td>
                        <td>@incomeSoldCourse_item.course_name</td>
                        <td>@incomeSoldCourse_item.course_price</td>
                        <td>@incomeSoldCourse_item.total_sold_course</td>
                        <td>@incomeSoldCourse_item.total_course_price</td>
                        <td>@incomeSoldCourse_item.service_charge</td>
                        <td>@incomeSoldCourse_item.total_income</td>
                        <td><a href='@Url.Action("StudentDetailBuyCourse", "StudentDetailBuyCourse", new {course_name = incomeSoldCourse_item.course_name})' class="btn btn-warning btn-sm"><i class="glyphicon glyphicon-search"></i>&nbsp;ดูรายละเอียด</a></td>
                    </tr>
                    num++;
                    total_course = total_course + @incomeSoldCourse_item.total_sold_course;
                    total_income = total_income + @incomeSoldCourse_item.total_income;
                }
            }

            @*<tr>


        </tr>*@

        </tbody>

        <tfoot>
            <tr>
                <td colspan="5" class="text-center" scope="row">รวมคอร์สที่ขายได้ทั้งหมด @total_course คอร์ส</td>
                <td colspan="4" class="text-center" scope="row">จำนวนเงินที่ได้รับทั้งหมด @total_income บาท</td>
            </tr>
        </tfoot>

    </table>

</div>
