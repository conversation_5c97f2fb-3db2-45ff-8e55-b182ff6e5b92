﻿@model IEnumerable<SE_Project.Models.Lecture>

@{
    ViewBag.Title = "MyLecture";
    var ID = ViewBag.ID;
    var course_name = ViewBag.course_name;
}

<h2>ชื่อคอร์ส : @course_name</h2>

<div class="well" style="margin-top: 50px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 4%; text-align:center;">Lecture No.</th>
                <th style="width: 19%; ">ชื่อเลคเชอร์</th>
                <th style="width: 1%; ">วีดีโอเลคเชอร์</th>
            </tr>

        </thead>
        <tbody>
            @{
                int num_lec = 0;
                foreach (var lec_Item in Model)
                {
                    num_lec++;
                    <tr>
                        <td style="text-align:center;">@num_lec</td>
                        <td>@lec_Item.lecture_Name</td>
                        <td>
                             <button type="button" class="btn btn-primary btn-sm" onclick="WatchVideo('@lec_Item.lecture_Name','@lec_Item.folderPath')"><i class="glyphicon glyphicon-search"></i>&nbsp;ดูวีดีโอ</button>
                        </td>
                    </tr>
                }
            }

        </tbody>

    </table>
</div>

@*====================================================== Pop-Up หน้าจอ ตรวจสอบ Lecture =======================================================*@
<div class="modal fade" id="WatchVideo_Modal" tabindex="-1" role="dialog" aria-labelledby="ModalTitle" aria-hidden="true">

    <div class="modal-dialog" style="width:820px;">
        <div class="modal-content ">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>

                <div class="modal-title">
                    <div class="form-group col-sm-12" style="font-size: 20px; margin-bottom: 0px;">
                        @*<div class="col-sm-1"></div>
                            <div class="col-sm-6">*@
                        @*</div>
                            <div class="col-sm-5">*@
                        <h4 id="lec_name"></h4>
                        @*</div>*@
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-top: 20px; text-align:center;">
                    <div class="form-group col-sm-12">

                        @*<p id="v_path"></p>*@
                        <div id="v_path">

                        </div>

                        @*<video width="800" controls src='' autoplay>

                            </video>*@

                    </div>
                </div>
            </div>


            <div class="modal-footer">
                <div class="text-left">
                    <div class="col-sm-10">
                        <div style="padding-top:0px; margin-left: 130px;" id="appr_lec">

                            @*<button class="btn btn-danger btn-lg">ไม่อนุมัติ Lecture</button>*@
                        </div>

                        @*<div class="col-md-6" style="padding-top:0px;">*@
                        @*<span style="color: red; font-size: 23px;"><b>*</b></span>
                            <textarea class="form-control" placeholder="กรอกเหตุผลที่ไม่อนุมัติ Lecture" style="height:43px;"></textarea>*@
                        @*<textarea type="text" class="form-control" placeholder="กรอกเหตุผลที่ไม่อนุมัติ Lecture" />*@
                        @*</div>*@
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<script>

    function WatchVideo(lecture_name, folder_path) {
        $("#lec_name").html('ชื่อเล็คเชอร์ : ' + lecture_name + '');
        $("#v_path").html("<video width='500' controls src='" + folder_path + "' ></video>");

        $("#WatchVideo_Modal").modal();
      
    }

    $('#WatchVideo_Modal').on('hidden.bs.modal', function () {
        $("#WatchVideo_Modal video").attr("src", $("#WatchVideo_Modal video").attr("src"));
        //$("#appr_lec").html('<button class="btn btn-success btn-block btn-lg btn_ApproveLecture" data-id=' + lec_id + '>อนุมัติ Lecture</button>').show();
        player.pause();
    });

</script>