﻿@model SE_Project.Models.ResetPasswordModel
@{
    ViewBag.Title = "ResetNewPassword";
    Layout = null;
}

@Scripts.Render("~/bundles/jquery")

@Scripts.Render("~/bundles/jqueryval")


<link href="~/Content/sweetalert.css" rel="stylesheet" />
<script src="~/Scripts/sweetalert-dev.js"></script>
<script src="~/Scripts/sweetalert.min.js"></script>

<link href="~/Content/bootstrap.min.css" rel="stylesheet" />

<div style="margin-bottom: 70px;"></div>

@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "resetNew_form" }))
{
    <div class="modal-dialog">
        <div class="modal-content ">
            <div class="modal-header">

                <h3 class="modal-title" style="text-align: left;">&nbsp;<u>เปลี่ยนรหัสผ่าน</u></h3>
            </div>

            <div class="row" style="margin-top: 20px;">

                <div class="form-group col-sm-12">
                    <label for="" class="col-sm-3 col-form-label" style="text-align: right;">รหัสผ่านใหม่ :</label>
                    <div class="col-sm-9">
                        <div class="md-form mt-0">
                            <input type="password" class="form-control" id="newPassword" name="newPassword" placeholder="รหัสผ่านใหม่">
                        </div>
                    </div>
                </div>
                <div class="form-group col-sm-12">
                    <label for="" class="col-sm-3 col-form-label" style="text-align: right;">ยืนยันรหัสผ่าน :</label>

                    <div class="col-sm-9">
                        <div class="md-form mt-0">
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="ยืนยันรหัสผ่านใหม่">

                            @Html.HiddenFor(model => model.email, new { id = "email" })
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer" style="text-align: center;">
                <button class="btn btn-primary" type="button" id="btnChangePassword"><i class="" style="font-size: 19px;"></i>&nbsp;<b>บันทึกการเปลี่ยนแปลง</b></button>
            </div>
        </div>
    </div>



    @Styles.Render("~/Content/css")

}
<style>
    input {
        max-width: 100%;
    }

    .error {
        color: red;
        border-color: red;
        box-shadow: 0px 0px 2px #ff0000;
        text-decoration-color: black;
    }
</style>


<script>


    $(document).ready(function () {
        $("#btnChangePassword").click(function () {

            var chk = CheckFormValidate();
            alert (chk);
            if (chk == true) {
                var user_email = $('#email').val();
                //alert(user_email);
                var new_password = $('#newPassword').val();
                //var password = $('#userPassword').val();
                //alert(email);
                $.ajax({
                    type: "POST",
                    url: "/ResetNewPassword/ResetNewPasswords",
                    data: { "user_email": user_email, "new_password": new_password },
                    //  data: data,
                    success: function (response) {
                        swal({
                            title: " ",
                            text: "บันทึกการเปลี่ยนแปลงรหัสผ่านสำเร็จ",
                            type: "success",
                            showConfirmButton: true
                        }, function () {
                            window.location.assign('/Home/Index');
                        });
                    }
                });
            }

            
        });
    });

    function CheckFormValidate() {

        if (!$("#resetNew_form").valid()) {
            return false;
        }
        return true;
    }

    $("#resetNew_form").validate({
        rules: {

            newPassword: {
                required: true,
                minlength: 6

            },
            confirmPassword: {
                equalTo: "#newPassword"
            }

        },
        messages: {

            newPassword: {
                required: "**กรุณากรอก Password",
                minlength: "กรอก 6 ตัวขึ้นไป"
                //passwordMatch: "**Password ไม่ตรงกัน"
            },
            confirmPassword: {
                //required: '**กรุณา ยืนยัน Password',
                equalTo: '**Password ไม่ตรงกัน'
                //passwordMatch: "**Password ไม่ตรงกัน",
                //minlength: "**Password ไม่ตรงกัน"
            }
        }
    });
</script>

