﻿@model IEnumerable<SE_Project.Models.Course>


@{
    ViewBag.Title = "Course List Instructor";
}



<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;คอร์สเรียนของผู้สอน</p>
</div>

<span style="font-size:20px;">ชื่อผู้สอน : Test@*@Session["user_Name"]*@</span>


<div class="well" style="margin-top: 20px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 13%; padding-bottom:40px;">วันที่สร้างคอร์ส</th>
                <th style="width: 24%; padding-bottom:40px;">ชื่อคอร์ส</th>
                <th style="width: 14%; padding-bottom:40px;">สถานะคอร์ส</th>
                <th style="width: 7%; padding-bottom:40px;">ราคา</th>

                <th style="width: 7%; padding-bottom:40px;">ตรวจสอบ</th>

            </tr>
            @*<tr>
                    <th scope="col" style="background-color:#ce93d8; width: 3%;">รวม</th>
                    <th scope="col" style="background-color:#ff4444; width: 10%;">ยังไม่ตรววจ Lecture</th>
                    <th scope="col" style="background-color:#00C851; width: 2%;">อนุมัติ Lecture</th>
                    <th scope="col" style="background-color:yellow; width: 2%;">สั่งแก้ Lecture</th>
                    <th scope="col" style="background-color:#ffbb33; width: 8%;">รอตรววจ Lecture</th>
                </tr>*@
        </thead>
        <tbody>
            @{
                foreach (var course_list in Model)
                {
                    <tr>
                        <td>@course_list.create_Date</td>
                        <td>@course_list.course_Name</td>
                        <td>@course_list.courseStatus_ID</td>
                        <td>@course_list.course_Price</td>

                        <td>
                            @*<a href="/Instructors/CourseListInstructorDetail?course_id = @cs.ID " class="btn btn-warning" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></a>*@
                            

                            @if (course_list.courseStatus_ID == 4) //ปุ่มตรวจสอบ จะ Disabled ก็ต่อเมื่อ คอร์สมีสถานะ ยกเลิกคอร์สโดยผู้สอน
                            {
                                <button class="btn btn-danger" disabled="" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button>
                            }
                            else
                            {
                                <a href='@Url.Action("CourseListInstructorDetail", "Instructors", new { course_id = course_list.ID ,course_name = course_list.course_Name})' class="btn btn-warning" style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></a>
                            }

                        </td>

                    </tr>
                }
            }

        </tbody>
    </table>
</div>

@*<tr>
        <td>19 พ.ค.2562</td>
        <td>การเขียนภาษาอังกฤษให้เก่งขั้นเทพ</td>
        <td>ยังไม่ตรวจคอร์ส</td>
        <td>1,500</td>
        <td style="text-align:center;">6</td>
        <td style="text-align:center;">6</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>

        <td><button class="btn btn-warning " style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button></td>

    </tr>
    <tr>
        <td>11 พ.ค.2562</td>
        <td>ติวสอบสำหรับสอบเข้ามหาวิทยาลัย</td>
        <td>กำลังตรวจคอร์ส</td>
        <td>3,000</td>
        <td style="text-align:center;">6</td>
        <td style="text-align:center;">2</td>
        <td style="text-align:center;">2</td>
        <td style="text-align:center;">1</td>
        <td style="text-align:center;">1</td>
        <td><button class="btn btn-warning " style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button></td>


    </tr>
    <tr>
        <td>20 เม.ย.2562</td>
        <td>เขียนโปรแกรมภาษา C# เบื้องต้น</td>
        <td>อนุมัติคอร์ส</td>
        <td>2,500</td>
        <td style="text-align:center;">8</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td><button class="btn btn-primary " style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button></td>
    </tr>
    <tr>
        <td>21 เม.ย.2562</td>
        <td>ฝึกพูดภาษาอังกฤษเพื่อการสื่อสาร</td>
        <td>อนุมัติคอร์ส</td>
        <td>1,900</td>
        <td style="text-align:center;">10</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td style="text-align:center;">0</td>
        <td><button class="btn btn-primary " style="font-size:12px;"><i class="glyphicon glyphicon-search"></i>&nbsp;<b>ตรวจสอบ</b></button></td>
    </tr>*@