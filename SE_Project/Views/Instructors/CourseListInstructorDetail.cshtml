﻿@model IEnumerable<SE_Project.Models.LectureViewModel>
@{
    ViewBag.Title = "CourseListInstructorDetail";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;ชื่อคอร์ส : @ViewBag.courseName</p>
</div>
<div class="text-right">
    @if (ViewBag.courseStatusID != 3 )  // ปุ่มยกเลิกคอร์ส จะไม่แสดงก็ต่อเมื่อ คอร์สมีสถานะ อนุมัติคอร์ส
    {
        <button class="btn btn-danger btnConfirm_CancelCourse" type="button" id="@ViewBag.courseID"><i class="glyphicon glyphicon-remove" style="font-size:16px;"></i>&nbsp;<b>ยกเลิกคอร์ส</b></button>
    }
    
</div>

<div class="well" style="margin-top: 20px;">
    <table class="table table-striped table-bordered table-hover" id="">
        <thead>
            <tr style="background-color:#64b5f6;">
                <th style="width: 4%; text-align:center;">Lecture No.</th>
                <th style="width: 19%; ">ชื่อ Lecture</th>
                <th style="width: 6%; ">สถานะ Lecture</th>
                <th style="width: 1%; ">แก้ไข Lecture</th>
            </tr>

        </thead>
        <tbody>
            @{
                int num_lec = 0;
                foreach (var lecture_list in Model)
                {
                    num_lec++;
                    //Session["numLec"] = num_lec;
                    <tr>
                        <td style="text-align:center;">@num_lec</td>
                        <td>@lecture_list.lecture_Name</td>
                        <td>@lecture_list.lectureStatus_Name</td>
                        <td>
                            @if (lecture_list.lectureStatus_ID == 4)  //อนุมัติเล็คเชอร์
                            {
                                <a href='@Url.Action("EditLecture", "Instructors", new { lecture_id = lecture_list.ID,course_name = lecture_list.course_Name })' class="btn btn-primary" style="font-size:12px;"><i class="glyphicon glyphicon-pencil"></i>&nbsp;<b>แก้ไข Lecture</b></a>

                            }
                            else if (lecture_list.lectureStatus_ID == 3) //รอตรวจเล็คเชอร์
                            {
                                <button class="btn btn-warning" disabled="" style="font-size:12px;"><i class="glyphicon glyphicon-pencil"></i>&nbsp;<b>แก้ไข Lecture</b></button>
                            }
                            else // lectureStatus_ID == 1 :ยังไม่ตรวจเล็คเชอร์ , lectureStatus_ID == 2 : สั่งแก้เล็คเชอร์
                            {
                                <a href='@Url.Action("EditLecture", "Instructors", new { lecture_id = lecture_list.ID,course_name = lecture_list.course_Name })' class="btn btn-warning" style="font-size:12px;"><i class="glyphicon glyphicon-pencil"></i>&nbsp;<b>แก้ไข Lecture</b></a>

                            }
                        </td>
                    </tr>
                }
            }
        </tbody>

    </table>
</div>



@*<script src="~/Scripts/jquery-3.3.1.min.js"></script>*@
<script>
    //$(document).on('click', '.confirmDelete', function () {

    //    $("#delete_Modal").modal();
    //    //alert(ins_id);
    //});
    $(document).on('click', '.btnConfirm_CancelCourse', function () {
        var course_id = $(this).attr("id");

        //$("#btnDelete").attr("delete-id", equ_Id);
        swal({
            title: "",
            text: "คุณต้องการยกเลิกคอร์สเรียนใช่หรือไม่ ?",
            type: "warning",
            confirmButtonText: "ตกลง",
            confirmButtonColor: "#d95940",
            showConfirmButton: true,
            showCancelButton: true
        }, function () {
            $.ajax({
                type: "POST",
                url: "/Instructors/CancelCourse",
                data: { "course_id": course_id },
                success: function () {
                    window.location.assign('/Instructors/CourseListInstructor');
                }
            });
        });

        alert(course_id);
    });

    $(document).ready(function () {
        $("#btnCancelCourse").click(function () {

            swal({
                title: "",
                text: "คุณต้องการยกเลิกคอร์สเรียนใช่หรือไม่ ?",
                type: "warning",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#d95940",
                showConfirmButton: true,
                showCancelButton: true
            });
        });
    });
</script>