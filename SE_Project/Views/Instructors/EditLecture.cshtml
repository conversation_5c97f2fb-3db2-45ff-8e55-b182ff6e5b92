﻿@model IEnumerable<SE_Project.Models.LectureViewModel>

@{
    ViewBag.Title = "EditLecture";
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-pencil"></i>&nbsp;แก้ไข Lecture</p>
</div>

@using (Html.BeginForm("EditLecture", "Instructors", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
    @Html.AntiForgeryToken()
    @*@{*@
        foreach (var lec in Model)
        {
            <div class="from-group col-md-12">
                <div class="col-md-7">
                    <span style="font-size:20px;">ชื่อคอร์ส : @ViewBag.courseName</span>
                </div>
                <div class="col-md-5">
                    <span style="font-size:20px;">สถานะปัจจุบัน : @lec.lectureStatus_Name</span>
                </div>
            </div>

            <div class="from-group col-md-12">
                <div class="col-md-6" style="margin-top: 10px;">
                    <span style="font-size:20px;">หมวดหมู่ : @lec.category_Name</span>
                </div>
                <div class="col-md-6"></div>
            </div>


            if (lec.lectureStatus_ID == 4)  //อนุมัติเล็คเชอร์
            {


                @*<div class="form-group col-md-12">

                        <div class="col-md-12" style="margin-top: 30px;">
                            <span style="font-size:20px;">เหตุผลที่ไม่อนุมัติ : </span>
                        </div>


                    </div>*@

                <div class="form-horizontal well form-group col-md-12" style="margin-top: 30px;">
                    <div class="form-group control-label">
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">Lecture <i style=" font-size: 20px;"></i>&nbsp;:</label>

                        </div>
                        <div class="col-md-5 control-label">
                            <input class="form-control" value="@lec.lecture_Name" disabled="disabled" />
                            @*<p class="form-control">@lec.lecture_Name</p>*@
                        </div>
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">เลือกไฟล์<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-3 control-label">
                            <span></span>
                            <input class="form-control" type="file" disabled="disabled" />


                        </div>
                    </div>
                    <div class="form-group control-label">
                        <div class="col-md-2">
                            <label class="col-form-label">Description<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-10">
                            <textarea class="form-control" style="height: 100px;" disabled="disabled">@lec.lecture_Description </textarea>

                        </div>
                    </div>
                    <div class=" text-right" style="margin-top:30px;">
                        <a href='@Url.Action("CourseListInstructorDetail", "Instructors", new { course_id = lec.course_ID ,course_name = ViewBag.courseName})' class="btn btn-info"><i class="glyphicon glyphicon-menu-left" style="font-size:14px;"></i>&nbsp;<b>ย้อนกลับไปหน้าคอร์สเรียน</b></a>

                    </div>
                </div>
                <label class="col-form-label " style="font-size: 18px; margin-top: 30px;"><u>ประวัติการแก้ไข Lecture</u></label>
                <div class="" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered table-hover" id="">
                        <thead>
                            <tr style="background-color:#ffbb33;">
                                <th style="width: 1%; text-align:center;">ลำดับ</th>
                                <th style="width: 20%; ">เหตุผล</th>
                                <th style="width: 2%; ">ชื่อผู้สั่งแก้</th>
                                <th style="width: 5%; ">วัน/เวลา ที่สั่งแก้</th>
                                <th style="width: 6%; ">วัน/เวลา ที่แก้ไขเสร็จ</th>
                            </tr>

                        </thead>
                        <tbody>
                            <tr>
                                <td style="text-align:center;">1</td>
                                <td></td>
                                <td>Admin1</td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            }
            else if (lec.lectureStatus_ID == 2)
            {
                <div class="form-group col-md-12">

                    <div class="col-md-12" style="margin-top: 30px;">
                        <span style="font-size:20px;">เหตุผลที่ไม่อนุมัติ : @lec.notAppr_Reason</span>
                    </div>


                </div>

                <div class="form-horizontal well form-group col-md-12">
                    <div class="form-group control-label">
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">Lecture <i style=" font-size: 20px;"></i>&nbsp;:</label>

                        </div>
                        <div class="col-md-5 control-label">
                            @Html.TextBox("lecture_name",lec.lecture_Name, new {@class = "form-control"}) 
                            @*<input class="form-control" value="@lec.lecture_Name" />*@
                        </div>
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">เลือกไฟล์<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-3 control-label">
                            @Html.TextBox("videofile", null, new { type = "file", multiple = "", @class = "form-control" })
                            @*<input class="form-control input" type="file" value="" />*@


                        </div>
                    </div>
                    <div class="form-group control-label"> 
                        <div class="col-md-2">
                            <label class="col-form-label">Description<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-10">
                            @*<textarea class="form-control" value="lecture_Description" style="height: 100px;">@lec.lecture_Description </textarea>*@
                            @Html.TextArea("lecture_description", lec.lecture_Description, new { @class = "form-control" })

                            @Html.Hidden("lecture_id", lec.ID)
                        </div>
                    </div>
                    <div class=" text-right" style="margin-top:30px;">
                        <button class="btn btn-success" type="submit"><i class="glyphicon glyphicon-save" style="font-size:16px;"></i>&nbsp;<b>ยืนยันการแก้ไข</b></button>
                    </div>
                </div>
                <label class="col-form-label " style="font-size: 18px; margin-top: 30px;"><u>ประวัติการแก้ไข Lecture</u></label>
                <div class="" style="margin-top: 20px;">
                    <table class="table table-striped table-bordered table-hover" id="">
                        <thead>
                            <tr style="background-color:#ffbb33;">
                                <th style="width: 1%; text-align:center;">ลำดับ</th>
                                <th style="width: 20%; ">เหตุผล</th>
                                <th style="width: 2%; ">ชื่อผู้สั่งแก้</th>
                                <th style="width: 5%; ">วัน/เวลา ที่สั่งแก้</th>
                                <th style="width: 6%; ">วัน/เวลา ที่แก้ไขเสร็จ</th>
                            </tr>

                        </thead>
                        <tbody>
                            <tr>
                                <td style="text-align:center;">1</td>
                                <td></td>
                                <td>Admin1</td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            }
            else   // lectureStatus_ID ==   :   ยังไม่ตรวจเล็คเชอร์
            {
                <div class="form-horizontal well form-group col-md-12" style="margin-top: 30px;">
                    <div class="form-group control-label">
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">Lecture <i style=" font-size: 20px;"></i>&nbsp;:</label>

                        </div>
                        <div class="col-md-5 control-label">
                            <input class="form-control" value="@lec.lecture_Name" />
                        </div>
                        <div class="col-md-2 control-label">
                            <label class="col-form-label">เลือกไฟล์<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-3 control-label">

                            <input class="form-control" type="file" id='' />


                        </div>
                    </div>
                    <div class="form-group control-label">
                        <div class="col-md-2">
                            <label class="col-form-label">Description<i style=" font-size: 20px;"></i>&nbsp;:</label>
                        </div>
                        <div class="col-md-10">
                            <textarea class="form-control" style="height: 100px;">@lec.lecture_Description </textarea>
                        </div>
                    </div>
                    <div class=" text-right" style="margin-top:30px;">
                        <button class="btn btn-success" type="button" id=""><i class="glyphicon glyphicon-save" style="font-size:16px;"></i>&nbsp;<b>ยืนยันการแก้ไข</b></button>
                    </div>
                </div>
            }


        }
    @*}*@
}




@*<label class="col-form-label " style="font-size: 18px; margin-top: 30px;"><u>ประวัติการแก้ไข Lecture</u></label>
    <div class="" style="margin-top: 20px;">
        <table class="table table-striped table-bordered table-hover" id="">
            <thead>
                <tr style="background-color:#ffbb33;">
                    <th style="width: 1%; text-align:center;">ลำดับ</th>
                    <th style="width: 20%; ">เหตุผล</th>
                    <th style="width: 2%; ">ชื่อผู้สั่งแก้</th>
                    <th style="width: 5%; ">วัน/เวลา ที่สั่งแก้</th>
                    <th style="width: 6%; ">วัน/เวลา ที่แก้ไขเสร็จ</th>
                </tr>

            </thead>
            <tbody>
                <tr>
                    <td style="text-align:center;">1</td>
                    <td></td>
                    <td>Admin1</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align:center;">2</td>
                    <td></td>
                    <td>Admin2</td>
                    <td></td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>*@



