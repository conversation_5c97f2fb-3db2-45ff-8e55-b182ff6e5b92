﻿@model IEnumerable<SE_Project.Models.DetailOfStudentByCourse>

@{
    ViewBag.Title = "StudentDetailBuyCourse";
    var course_name = ViewBag.course_name;
}

<div class="alert-info">
    <p style="font-size:26px;">&nbsp;<i class="glyphicon glyphicon-list"></i>&nbsp;รายละเอียดผู้เรียน</p>
</div>


<h3>ชื่อคอร์สเรียน : @course_name</h3>

<div class="well" style="margin-top: 20px;">
    <table class="table table-striped table-bordered table-hover" id="example">
        <thead>
            <tr style="background-color:deepskyblue;">
                <th style="text-align:center;">ลำดับ</th>
                <th style="">ชื่อผู้เรียน</th>
                <th style="">Email</th>
                <th style="">วัน/เวลา ที่ซื้อคอร์ส</th>
            </tr>

        </thead>
        <tbody>

            @{
                int num = 1;
                foreach (var details in Model)
                {
                    <tr>
                        <td style="text-align:center;">@num</td>
                        <td>@details.student_name</td>
                        <td>@details.student_Email</td>
                        <td>@details.dateTime_OrderCourse</td>
                    </tr>
                    num++;
                }
            }

        </tbody>

    </table>
</div>

