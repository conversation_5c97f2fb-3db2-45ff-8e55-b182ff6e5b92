.vs/SE_Project/config/applicationhost.config
.vs/SE_Project/v16/.suo
.vs/SE_Project/v16/Server/sqlite3/db.lock
.vs/SE_Project/v16/Server/sqlite3/storage.ide
packages/Antlr.*******/.signature.p7s
packages/Antlr.*******/Antlr.*******.nupkg
packages/Antlr.*******/lib/Antlr3.Runtime.dll
packages/Antlr.*******/lib/Antlr3.Runtime.pdb
packages/bootstrap.3.3.7/.signature.p7s
packages/bootstrap.3.3.7/bootstrap.3.3.7.nupkg
packages/bootstrap.3.3.7/content/Content/bootstrap.css
packages/bootstrap.3.3.7/content/Content/bootstrap.css.map
packages/bootstrap.3.3.7/content/Content/bootstrap.min.css
packages/bootstrap.3.3.7/content/Content/bootstrap.min.css.map
packages/bootstrap.3.3.7/content/Content/bootstrap-theme.css
packages/bootstrap.3.3.7/content/Content/bootstrap-theme.css.map
packages/bootstrap.3.3.7/content/Content/bootstrap-theme.min.css
packages/bootstrap.3.3.7/content/Content/bootstrap-theme.min.css.map
packages/bootstrap.3.3.7/content/fonts/glyphicons-halflings-regular.eot
packages/bootstrap.3.3.7/content/fonts/glyphicons-halflings-regular.svg
packages/bootstrap.3.3.7/content/fonts/glyphicons-halflings-regular.ttf
packages/bootstrap.3.3.7/content/fonts/glyphicons-halflings-regular.woff
packages/bootstrap.3.3.7/content/fonts/glyphicons-halflings-regular.woff2
packages/bootstrap.3.3.7/content/Scripts/bootstrap.js
packages/bootstrap.3.3.7/content/Scripts/bootstrap.min.js
packages/EntityFramework.6.2.0/.signature.p7s
packages/EntityFramework.6.2.0/EntityFramework.6.2.0.nupkg
packages/EntityFramework.6.2.0/content/net40/App.config.transform
packages/EntityFramework.6.2.0/content/net40/Web.config.transform
packages/EntityFramework.6.2.0/lib/net40/EntityFramework.dll
packages/EntityFramework.6.2.0/lib/net40/EntityFramework.SqlServer.dll
packages/EntityFramework.6.2.0/lib/net40/EntityFramework.SqlServer.xml
packages/EntityFramework.6.2.0/lib/net40/EntityFramework.xml
packages/EntityFramework.6.2.0/lib/net45/EntityFramework.dll
packages/EntityFramework.6.2.0/lib/net45/EntityFramework.SqlServer.dll
packages/EntityFramework.6.2.0/lib/net45/EntityFramework.SqlServer.xml
packages/EntityFramework.6.2.0/lib/net45/EntityFramework.xml
packages/EntityFramework.6.2.0/tools/about_EntityFramework.help.txt
packages/EntityFramework.6.2.0/tools/EntityFramework.PowerShell.dll
packages/EntityFramework.6.2.0/tools/EntityFramework.PowerShell.Utility.dll
packages/EntityFramework.6.2.0/tools/EntityFramework.psd1
packages/EntityFramework.6.2.0/tools/EntityFramework.psm1
packages/EntityFramework.6.2.0/tools/init.ps1
packages/EntityFramework.6.2.0/tools/install.ps1
packages/EntityFramework.6.2.0/tools/migrate.exe
packages/HtmlAgilityPack.1.11.22/.signature.p7s
packages/HtmlAgilityPack.1.11.22/HtmlAgilityPack.1.11.22.nupkg
packages/HtmlAgilityPack.1.11.22/lib/Net35/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/Net35/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/Net35/HtmlAgilityPack.xml
packages/HtmlAgilityPack.1.11.22/lib/Net40/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/Net40/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/Net40/HtmlAgilityPack.XML
packages/HtmlAgilityPack.1.11.22/lib/Net40-client/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/Net40-client/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/Net40-client/HtmlAgilityPack.xml
packages/HtmlAgilityPack.1.11.22/lib/Net45/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/Net45/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/Net45/HtmlAgilityPack.XML
packages/HtmlAgilityPack.1.11.22/lib/NetCore45/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/NetCore45/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/NetCore45/HtmlAgilityPack.XML
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.3/HtmlAgilityPack.deps.json
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.3/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.3/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.3/HtmlAgilityPack.xml
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.6/HtmlAgilityPack.deps.json
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.6/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.6/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/netstandard1.6/HtmlAgilityPack.xml
packages/HtmlAgilityPack.1.11.22/lib/netstandard2.0/HtmlAgilityPack.deps.json
packages/HtmlAgilityPack.1.11.22/lib/netstandard2.0/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/netstandard2.0/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/netstandard2.0/HtmlAgilityPack.xml
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML
packages/HtmlAgilityPack.1.11.22/lib/uap10.0/HtmlAgilityPack.dll
packages/HtmlAgilityPack.1.11.22/lib/uap10.0/HtmlAgilityPack.pdb
packages/HtmlAgilityPack.1.11.22/lib/uap10.0/HtmlAgilityPack.pri
packages/HtmlAgilityPack.1.11.22/lib/uap10.0/HtmlAgilityPack.XML
packages/jQuery.3.3.1/.signature.p7s
packages/jQuery.3.3.1/jQuery.3.3.1.nupkg
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.js
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.min.js
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.min.map
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.slim.js
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.slim.min.js
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1.slim.min.map
packages/jQuery.3.3.1/Content/Scripts/jquery-3.3.1-vsdoc.js
packages/jQuery.3.3.1/Tools/common.ps1
packages/jQuery.3.3.1/Tools/install.ps1
packages/jQuery.3.3.1/Tools/jquery-3.3.1.intellisense.js
packages/jQuery.3.3.1/Tools/uninstall.ps1
packages/jQuery.Validation.1.17.0/.signature.p7s
packages/jQuery.Validation.1.17.0/jQuery.Validation.1.17.0.nupkg
packages/jQuery.Validation.1.17.0/Content/Scripts/jquery.validate.js
packages/jQuery.Validation.1.17.0/Content/Scripts/jquery.validate.min.js
packages/jQuery.Validation.1.17.0/Content/Scripts/jquery.validate-vsdoc.js
packages/Microsoft.AspNet.Mvc.5.2.4/.signature.p7s
packages/Microsoft.AspNet.Mvc.5.2.4/Microsoft.AspNet.Mvc.5.2.4.nupkg
packages/Microsoft.AspNet.Mvc.5.2.4/Content/Web.config.install.xdt
packages/Microsoft.AspNet.Mvc.5.2.4/Content/Web.config.uninstall.xdt
packages/Microsoft.AspNet.Mvc.5.2.4/lib/net45/System.Web.Mvc.dll
packages/Microsoft.AspNet.Mvc.5.2.4/lib/net45/System.Web.Mvc.xml
packages/Microsoft.AspNet.Razor.3.2.4/.signature.p7s
packages/Microsoft.AspNet.Razor.3.2.4/Microsoft.AspNet.Razor.3.2.4.nupkg
packages/Microsoft.AspNet.Razor.3.2.4/lib/net45/System.Web.Razor.dll
packages/Microsoft.AspNet.Razor.3.2.4/lib/net45/System.Web.Razor.xml
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/.signature.p7s
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/Microsoft.AspNet.TelemetryCorrelation.1.0.0.nupkg
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/content/net45/web.config.install.xdt
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/content/net45/web.config.uninstall.xdt
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/lib/net45/Microsoft.AspNet.TelemetryCorrelation.dll
packages/Microsoft.AspNet.TelemetryCorrelation.1.0.0/lib/net45/Microsoft.AspNet.TelemetryCorrelation.xml
packages/Microsoft.AspNet.Web.Optimization.1.1.3/.signature.p7s
packages/Microsoft.AspNet.Web.Optimization.1.1.3/Microsoft.AspNet.Web.Optimization.1.1.3.nupkg
packages/Microsoft.AspNet.Web.Optimization.1.1.3/lib/net40/System.Web.Optimization.dll
packages/Microsoft.AspNet.Web.Optimization.1.1.3/lib/net40/system.web.optimization.xml
packages/Microsoft.AspNet.WebPages.3.2.4/.signature.p7s
packages/Microsoft.AspNet.WebPages.3.2.4/Microsoft.AspNet.WebPages.3.2.4.nupkg
packages/Microsoft.AspNet.WebPages.3.2.4/Content/Web.config.install.xdt
packages/Microsoft.AspNet.WebPages.3.2.4/Content/Web.config.uninstall.xdt
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.Helpers.dll
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.Helpers.xml
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.Deployment.dll
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.Deployment.xml
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.dll
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.Razor.dll
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.Razor.xml
packages/Microsoft.AspNet.WebPages.3.2.4/lib/net45/System.Web.WebPages.xml
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/.signature.p7s
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0.nupkg
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/build/net45/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.Extensions.props
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/build/net45/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/build/net46/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.Extensions.props
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/build/net46/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net45/app.config.install.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net45/app.config.uninstall.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net45/web.config.install.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net45/web.config.uninstall.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net46/app.config.install.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net46/app.config.uninstall.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net46/web.config.install.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/content/net46/web.config.uninstall.xdt
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/lib/net45/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/lib/net45/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/net45/install.ps1
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/net45/uninstall.ps1
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/csc.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/csc.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/csc.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/csi.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/csi.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.Build.Tasks.CodeAnalysis.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CodeAnalysis.CSharp.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CodeAnalysis.CSharp.Scripting.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CodeAnalysis.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CodeAnalysis.Scripting.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CodeAnalysis.VisualBasic.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.CSharp.Core.targets
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.DiaSymReader.Native.amd64.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.DiaSymReader.Native.x86.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/Microsoft.VisualBasic.Core.targets
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.AppContext.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.Collections.Immutable.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.Diagnostics.StackTrace.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.IO.FileSystem.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.IO.FileSystem.Primitives.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/System.Reflection.Metadata.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/vbc.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/vbc.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/vbc.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/VBCSCompiler.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/Roslyn45/VBCSCompiler.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csc.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csc.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csc.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csi.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csi.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/csi.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.Build.Tasks.CodeAnalysis.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CodeAnalysis.CSharp.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CodeAnalysis.CSharp.Scripting.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CodeAnalysis.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CodeAnalysis.Scripting.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CodeAnalysis.VisualBasic.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.CSharp.Core.targets
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.DiaSymReader.Native.amd64.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.DiaSymReader.Native.x86.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.Managed.Core.targets
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/Microsoft.VisualBasic.Core.targets
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.AppContext.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Collections.Immutable.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Console.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Diagnostics.FileVersionInfo.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Diagnostics.StackTrace.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.IO.Compression.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.IO.FileSystem.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.IO.FileSystem.Primitives.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.IO.Pipes.AccessControl.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.IO.Pipes.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Reflection.Metadata.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.AccessControl.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Claims.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Cryptography.Algorithms.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Cryptography.Encoding.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Cryptography.Primitives.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Cryptography.X509Certificates.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Security.Principal.Windows.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Text.Encoding.CodePages.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.ValueTuple.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Xml.ReaderWriter.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Xml.XmlDocument.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Xml.XPath.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/System.Xml.XPath.XDocument.dll
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/vbc.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/vbc.exe.config
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/vbc.rsp
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/VBCSCompiler.exe
packages/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0/tools/RoslynLatest/VBCSCompiler.exe.config
packages/Microsoft.jQuery.Unobtrusive.Validation.3.2.4/.signature.p7s
packages/Microsoft.jQuery.Unobtrusive.Validation.3.2.4/Microsoft.jQuery.Unobtrusive.Validation.3.2.4.nupkg
packages/Microsoft.jQuery.Unobtrusive.Validation.3.2.4/Content/Scripts/jquery.validate.unobtrusive.js
packages/Microsoft.jQuery.Unobtrusive.Validation.3.2.4/Content/Scripts/jquery.validate.unobtrusive.min.js
packages/Microsoft.Web.Infrastructure.*******/.signature.p7s
packages/Microsoft.Web.Infrastructure.*******/Microsoft.Web.Infrastructure.*******.nupkg
packages/Microsoft.Web.Infrastructure.*******/lib/net40/Microsoft.Web.Infrastructure.dll
packages/Modernizr.2.8.3/.signature.p7s
packages/Modernizr.2.8.3/Modernizr.2.8.3.nupkg
packages/Modernizr.2.8.3/Content/Scripts/modernizr-2.8.3.js
packages/Modernizr.2.8.3/Tools/common.ps1
packages/Modernizr.2.8.3/Tools/install.ps1
packages/Modernizr.2.8.3/Tools/uninstall.ps1
packages/Newtonsoft.Json.11.0.1/.signature.p7s
packages/Newtonsoft.Json.11.0.1/LICENSE.md
packages/Newtonsoft.Json.11.0.1/Newtonsoft.Json.11.0.1.nupkg
packages/Newtonsoft.Json.11.0.1/lib/net20/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/net20/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/net35/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/net35/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/net40/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/net40/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/net45/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/net45/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/netstandard1.0/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/netstandard1.0/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/netstandard1.3/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/netstandard1.3/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/netstandard2.0/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/netstandard2.0/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml
packages/Newtonsoft.Json.11.0.1/lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll
packages/Newtonsoft.Json.11.0.1/lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/.signature.p7s
packages/System.Diagnostics.DiagnosticSource.4.4.1/LICENSE.TXT
packages/System.Diagnostics.DiagnosticSource.4.4.1/System.Diagnostics.DiagnosticSource.4.4.1.nupkg
packages/System.Diagnostics.DiagnosticSource.4.4.1/THIRD-PARTY-NOTICES.TXT
packages/System.Diagnostics.DiagnosticSource.4.4.1/useSharedDesignerContext.txt
packages/System.Diagnostics.DiagnosticSource.4.4.1/version.txt
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/net45/System.Diagnostics.DiagnosticSource.dll
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/net45/System.Diagnostics.DiagnosticSource.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/net46/System.Diagnostics.DiagnosticSource.dll
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/net46/System.Diagnostics.DiagnosticSource.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/netcoreapp2.0/_._
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll
packages/System.Diagnostics.DiagnosticSource.4.4.1/lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml
packages/System.Diagnostics.DiagnosticSource.4.4.1/ref/netcoreapp2.0/_._
packages/WebGrease.1.6.0/.signature.p7s
packages/WebGrease.1.6.0/WebGrease.1.6.0.nupkg
packages/WebGrease.1.6.0/lib/WebGrease.dll
packages/WebGrease.1.6.0/tools/WG.EXE
SE_Project/bin/Antlr3.Runtime.dll
SE_Project/bin/Antlr3.Runtime.pdb
SE_Project/bin/EntityFramework.dll
SE_Project/bin/EntityFramework.SqlServer.dll
SE_Project/bin/EntityFramework.SqlServer.xml
SE_Project/bin/EntityFramework.xml
SE_Project/bin/HtmlAgilityPack.dll
SE_Project/bin/HtmlAgilityPack.pdb
SE_Project/bin/HtmlAgilityPack.xml
SE_Project/bin/Microsoft.AspNet.TelemetryCorrelation.dll
SE_Project/bin/Microsoft.AspNet.TelemetryCorrelation.xml
SE_Project/bin/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
SE_Project/bin/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
SE_Project/bin/Microsoft.Web.Infrastructure.dll
SE_Project/bin/Newtonsoft.Json.dll
SE_Project/bin/Newtonsoft.Json.xml
SE_Project/bin/SE_Project.dll
SE_Project/bin/SE_Project.dll.config
SE_Project/bin/SE_Project.pdb
SE_Project/bin/System.Diagnostics.DiagnosticSource.dll
SE_Project/bin/System.Diagnostics.DiagnosticSource.xml
SE_Project/bin/System.Web.Helpers.dll
SE_Project/bin/System.Web.Helpers.xml
SE_Project/bin/System.Web.Mvc.dll
SE_Project/bin/System.Web.Mvc.xml
SE_Project/bin/System.Web.Optimization.dll
SE_Project/bin/System.Web.Optimization.xml
SE_Project/bin/System.Web.Razor.dll
SE_Project/bin/System.Web.Razor.xml
SE_Project/bin/System.Web.WebPages.Deployment.dll
SE_Project/bin/System.Web.WebPages.Deployment.xml
SE_Project/bin/System.Web.WebPages.dll
SE_Project/bin/System.Web.WebPages.Razor.dll
SE_Project/bin/System.Web.WebPages.Razor.xml
SE_Project/bin/System.Web.WebPages.xml
SE_Project/bin/WebGrease.dll
SE_Project/bin/roslyn/csc.exe
SE_Project/bin/roslyn/csc.exe.config
SE_Project/bin/roslyn/csc.rsp
SE_Project/bin/roslyn/csi.exe
SE_Project/bin/roslyn/csi.exe.config
SE_Project/bin/roslyn/csi.rsp
SE_Project/bin/roslyn/Microsoft.Build.Tasks.CodeAnalysis.dll
SE_Project/bin/roslyn/Microsoft.CodeAnalysis.CSharp.dll
SE_Project/bin/roslyn/Microsoft.CodeAnalysis.CSharp.Scripting.dll
SE_Project/bin/roslyn/Microsoft.CodeAnalysis.dll
SE_Project/bin/roslyn/Microsoft.CodeAnalysis.Scripting.dll
SE_Project/bin/roslyn/Microsoft.CodeAnalysis.VisualBasic.dll
SE_Project/bin/roslyn/Microsoft.CSharp.Core.targets
SE_Project/bin/roslyn/Microsoft.DiaSymReader.Native.amd64.dll
SE_Project/bin/roslyn/Microsoft.DiaSymReader.Native.x86.dll
SE_Project/bin/roslyn/Microsoft.Managed.Core.targets
SE_Project/bin/roslyn/Microsoft.VisualBasic.Core.targets
SE_Project/bin/roslyn/System.AppContext.dll
SE_Project/bin/roslyn/System.Collections.Immutable.dll
SE_Project/bin/roslyn/System.Console.dll
SE_Project/bin/roslyn/System.Diagnostics.FileVersionInfo.dll
SE_Project/bin/roslyn/System.Diagnostics.StackTrace.dll
SE_Project/bin/roslyn/System.IO.Compression.dll
SE_Project/bin/roslyn/System.IO.FileSystem.dll
SE_Project/bin/roslyn/System.IO.FileSystem.Primitives.dll
SE_Project/bin/roslyn/System.IO.Pipes.AccessControl.dll
SE_Project/bin/roslyn/System.IO.Pipes.dll
SE_Project/bin/roslyn/System.Reflection.Metadata.dll
SE_Project/bin/roslyn/System.Security.AccessControl.dll
SE_Project/bin/roslyn/System.Security.Claims.dll
SE_Project/bin/roslyn/System.Security.Cryptography.Algorithms.dll
SE_Project/bin/roslyn/System.Security.Cryptography.Encoding.dll
SE_Project/bin/roslyn/System.Security.Cryptography.Primitives.dll
SE_Project/bin/roslyn/System.Security.Cryptography.X509Certificates.dll
SE_Project/bin/roslyn/System.Security.Principal.Windows.dll
SE_Project/bin/roslyn/System.Text.Encoding.CodePages.dll
SE_Project/bin/roslyn/System.ValueTuple.dll
SE_Project/bin/roslyn/System.Xml.ReaderWriter.dll
SE_Project/bin/roslyn/System.Xml.XmlDocument.dll
SE_Project/bin/roslyn/System.Xml.XPath.dll
SE_Project/bin/roslyn/System.Xml.XPath.XDocument.dll
SE_Project/bin/roslyn/vbc.exe
SE_Project/bin/roslyn/vbc.exe.config
SE_Project/bin/roslyn/vbc.rsp
SE_Project/bin/roslyn/VBCSCompiler.exe
SE_Project/bin/roslyn/VBCSCompiler.exe.config
SE_Project/obj/Debug/DesignTimeResolveAssemblyReferencesInput.cache
SE_Project/obj/Debug/SE_Project.csproj.CopyComplete
SE_Project/obj/Debug/SE_Project.csproj.FileListAbsolute.txt
SE_Project/obj/Debug/SE_Project.csprojAssemblyReference.cache
SE_Project/obj/Debug/SE_Project.dll
SE_Project/obj/Debug/SE_Project.pdb
SE_Project/obj/Debug/edmxResourcesToEmbed/Models/Model1.csdl
SE_Project/obj/Debug/edmxResourcesToEmbed/Models/Model1.msl
SE_Project/obj/Debug/edmxResourcesToEmbed/Models/Model1.ssdl
.vs/config/applicationhost.config
.vs/SE_Project/v15/.suo
.vs/SE_Project/v15/Server/sqlite3/db.lock
.vs/SE_Project/v15/Server/sqlite3/storage.ide
.vs/SE_Project/v15/Server/sqlite3/storage.ide-shm
.vs/SE_Project/v15/Server/sqlite3/storage.ide-wal
SE_Project/SE_Project.csproj
SE_Project/obj/Debug/SE_Project.csproj.CoreCompileInputs.cache
SE_Project/obj/Debug/TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs
SE_Project/obj/Debug/TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs
SE_Project/obj/Debug/TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs
/.vs/SE_Project/v16/Server/sqlite3
